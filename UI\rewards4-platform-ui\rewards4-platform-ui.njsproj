<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">14.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <Name>rewards4-platform-ui</Name>
    <RootNamespace>rewards4-platform-ui-vue</RootNamespace>
    <SaveNodeJsSettingsInProjectFile>True</SaveNodeJsSettingsInProjectFile>
    <ScriptArguments>serve</ScriptArguments>
  </PropertyGroup>
  <Target Name="Build">
    <Exec Command="npm install" />
    <Exec Command="npm run build" />
  </Target>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{807d04ab-197e-494c-b51c-0c489436a847}</ProjectGuid>
    <ProjectHome>
    </ProjectHome>
    <StartupFile>node_modules\@vue\cli-service\bin\vue-cli-service.js</StartupFile>
    <StartWebBrowser>True</StartWebBrowser>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <ProjectTypeGuids>{3AF33F2E-1136-4D97-BBB7-1795711AC8B8};{349c5851-65df-11da-9384-00065b846f21};{9092AA53-FB77-4645-B42D-1CCCA6BD08BD}</ProjectTypeGuids>
    <NodejsPort>1337</NodejsPort>
    <EnableTypeScript>true</EnableTypeScript>
    <StartWebBrowser>True</StartWebBrowser>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <None Include="server.ts" />
    <Content Include=".gitignore" />
    <Content Include="public\favicon.ico" />
    <Content Include="src\App.vue" />
    <Content Include="src\assets\base.css" />
    <Content Include="src\assets\images\logos\LincolnCityLogo.svg" />
    <Content Include="src\assets\images\TEMPDEV\arrow-up-right-from-square-solid-full.svg" />
    <Content Include="src\assets\images\TEMPDEV\boots-new-header-jun25.jpg" />
    <Content Include="src\assets\images\TEMPDEV\bqsqa.png" />
    <Content Include="src\assets\images\TEMPDEV\circle-check-regular-full.svg" />
    <Content Include="src\assets\images\TEMPDEV\curryssq.png" />
    <Content Include="src\assets\images\TEMPDEV\dunelm-sq.png" />
    <Content Include="src\assets\images\TEMPDEV\dunelm_banner_2025_summer.jpg" />
    <Content Include="src\assets\images\TEMPDEV\eae3b9cc-dfe0-45d4-a416-1bc59fb65335-Lincoln_HowItWorks_Click.png" />
    <Content Include="src\assets\images\TEMPDEV\ebaysq.png" />
    <Content Include="src\assets\images\TEMPDEV\expedia-may-23-sq.png" />
    <Content Include="src\assets\images\TEMPDEV\facebook-50x50.png" />
    <Content Include="src\assets\images\TEMPDEV\facebook-f-brands-solid-full.svg" />
    <Content Include="src\assets\images\TEMPDEV\facebook-w.png" />
    <Content Include="src\assets\images\TEMPDEV\facebook1-w.png" />
    <Content Include="src\assets\images\TEMPDEV\faqs-icon.svg" />
    <Content Include="src\assets\images\TEMPDEV\gamcare-white_285x100 %283%29.png" />
    <Content Include="src\assets\images\TEMPDEV\HowtoCollect_PageRoundel_1.png" />
    <Content Include="src\assets\images\TEMPDEV\Imps_TestimonialImage_1.png" />
    <Content Include="src\assets\images\TEMPDEV\just-eat-new-header-nov24.jpg" />
    <Content Include="src\assets\images\TEMPDEV\justeatsquare2.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln-3-shoponline.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_CollectPoints_ClubPartner_DesktopCarousel.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_Collect_Retailers_1000x420.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_HomepageLoggedOut_SignIn_HeroBanner_Desktop.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_HomepageLoggedOut_SignIn_HeroBanner_Desktop1.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_HowItWorks_Click %281%29.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_HowtoCollectPoints_2025_Premiere.mp4" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_Needsomehelp_DesktopStrip_1100x120.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_RAF_DesktopCarousel_1000x420.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_SpecialOffers_Carousel_Desktop_1000x420.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_SpecialOffers_Carousel_Mobile_650x800.png" />
    <Content Include="src\assets\images\TEMPDEV\Lincoln_Win_Desktop.png" />
    <Content Include="src\assets\images\TEMPDEV\Mail-icon.svg" />
    <Content Include="src\assets\images\TEMPDEV\marksandspencersquare2207.png" />
    <Content Include="src\assets\images\TEMPDEV\marksandspencer_merchant_header_banner_01122023-2.jpg" />
    <Content Include="src\assets\images\TEMPDEV\moonpig-square.png" />
    <Content Include="src\assets\images\TEMPDEV\moonpigbanner.jpg" />
    <Content Include="src\assets\images\TEMPDEV\MyProfile.svg" />
    <Content Include="src\assets\images\TEMPDEV\NotSetGoal_MatchTickets_500x500px.png" />
    <Content Include="src\assets\images\TEMPDEV\NotSetGoal_Membership_500x500px.png" />
    <Content Include="src\assets\images\TEMPDEV\NotSetGoal_Merch_500x500px.png" />
    <Content Include="src\assets\images\TEMPDEV\NotSetGoal_SeasonMembership_500x500px.png" />
    <Content Include="src\assets\images\TEMPDEV\quote svg.svg" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendGoalTile_GoldMembership_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendTIle_Hospitality_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendTile_MatchTickets_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendTile_Memberships_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendTile_Merch_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\SpendTile_WinPrizes_800x400px.png" />
    <Content Include="src\assets\images\TEMPDEV\Spend\WSP-SpendTile-820x369-generic.png" />
    <Content Include="src\assets\images\TEMPDEV\square_logobootsnew.png" />
    <Content Include="src\assets\images\TEMPDEV\TP-Logo-Monochrome-Black.png" />
    <Content Include="src\assets\images\TEMPDEV\TP-Logo-Monochrome-White.png" />
    <Content Include="src\assets\images\TEMPDEV\trustpilot.png" />
    <Content Include="src\assets\images\TEMPDEV\twitter-50x50 %281%29.png" />
    <Content Include="src\assets\images\TEMPDEV\Twitter-w.png" />
    <Content Include="src\assets\images\TEMPDEV\user-regular-full.svg" />
    <Content Include="src\assets\images\TEMPDEV\verylogosq.png" />
    <Content Include="src\assets\images\TEMPDEV\verymerchantbannerv2030425.png" />
    <Content Include="src\assets\images\TEMPDEV\Wallet-icon.svg" />
    <Content Include="src\assets\images\TEMPDEV\wilko-desktop.jpg" />
    <Content Include="src\assets\images\TEMPDEV\wilko-mobile.jpg" />
    <Content Include="src\assets\images\TEMPDEV\wilkologo.png" />
    <Content Include="src\assets\images\TEMPDEV\x-twitter-brands-solid-full.svg" />
    <Content Include="src\assets\logo.svg" />
    <Content Include="src\assets\main.css" />
    <Content Include="src\components\123Steps.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\Carousel.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\CategorgyFilter1.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\CategoryFilter.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\ContactPreferences.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\CTA.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\Faqs.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\FooterLinks.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\FooterSocialMedia.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\GambleAware.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\HeroCarousel.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\homeView.vue" />
    <Content Include="src\components\Login.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\MasonryCards.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\MasonrySpendCards.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\MyGoal.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\MyProfile.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\MyStatement.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\RetailerCards.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\RetailerTerms.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\IndexPageBrands.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\SignedOutCollect&amp;Spend.js">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\IndexPageCollect.vue" />
    <Content Include="src\components\IndexPageHero.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\IndexPageSpend.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\SigninView.vue" />
    <Content Include="src\components\Signup.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\SignupView.vue" />
    <Content Include="src\components\SiteFooter.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\SiteNav.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\SpendMyPoints.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\StripBanner.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\TopBrandsCards.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\components\Video.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\ChangePasswordView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\CollectHomeView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\CollectRetailerView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\ForgotPasswordView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\HelpCentreView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\IndexPageView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\HowItWorksView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\JoinSignUpView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\HomePageView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\MyAccountView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\MyGoalView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\SiteFooterView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\SpendOnView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\SpendView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="src\views\WeeklyTriviaView.vue">
      <SubType>Code</SubType>
    </Content>
    <Content Include="tsconfig.json" />
    <Content Include="package.json" />
    <Content Include="README.md" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="src\" />
    <Folder Include="public\" />
    <Folder Include="src\assets\" />
    <Folder Include="src\assets\images\" />
    <Folder Include="src\assets\images\logos\" />
    <Folder Include="src\assets\images\TEMPDEV\" />
    <Folder Include="src\assets\images\TEMPDEV\Spend\" />
    <Folder Include="src\components\" />
    <Folder Include="src\constants\" />
    <Folder Include="src\utils\" />
    <Folder Include="src\responses\" />
    <Folder Include="src\views\" />
    <Folder Include="src\requests\" />
    <Folder Include="src\services\" />
  </ItemGroup>
  <ItemGroup>
    <TypeScriptCompile Include="src\constants\Tenants.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\constants\Urls.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\main.ts" />
    <TypeScriptCompile Include="src\requests\SigninRequest.ts" />
    <TypeScriptCompile Include="src\requests\SingupRequest.ts" />
    <TypeScriptCompile Include="src\responses\ApiResponse.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\responses\IndexPageDataResponse.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\responses\SigninResponse.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\responses\SignupResponse.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\router.ts" />
    <TypeScriptCompile Include="src\services\authentication-service.ts" />
    <TypeScriptCompile Include="src\services\axios.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\services\page-service.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
    <TypeScriptCompile Include="src\services\tenant-service.ts" />
    <TypeScriptCompile Include="src\shims-vue.d.ts" />
    <TypeScriptCompile Include="src\utils\global.ts">
      <SubType>Code</SubType>
    </TypeScriptCompile>
  </ItemGroup>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>False</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:48022/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>True</UseCustomServer>
          <CustomServerUrl>http://localhost:1337</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}" User="">
        <WebProjectProperties>
          <StartPageUrl>
          </StartPageUrl>
          <StartAction>CurrentPage</StartAction>
          <AspNetDebugging>True</AspNetDebugging>
          <SilverlightDebugging>False</SilverlightDebugging>
          <NativeDebugging>False</NativeDebugging>
          <SQLDebugging>False</SQLDebugging>
          <ExternalProgram>
          </ExternalProgram>
          <StartExternalURL>
          </StartExternalURL>
          <StartCmdLineArguments>
          </StartCmdLineArguments>
          <StartWorkingDirectory>
          </StartWorkingDirectory>
          <EnableENC>False</EnableENC>
          <AlwaysStartWebServerOnDebug>False</AlwaysStartWebServerOnDebug>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>