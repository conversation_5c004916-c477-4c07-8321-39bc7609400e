<template>
    <section>
        <div class="container">
            <div class="grid">
                <footerLinks></footerLinks>
                <socialMedia></socialMedia>





                <p>
                    <small>&copy; Copyright 2018 R4G LTD. Registered in England and Wales No. 07347585. All rights reserved. Address for all correspondence and queries: R4G Ltd, Suite 5 - 6 Faraday Court, Centrum 100, Burton on Trent, Staffordshire, DE14 2WX. Registered under the Data Protection Act.R4G LTD - Registration No.Z2953491.</small>
                </p>

                <gambleAware></gambleAware>

            </div>
        </div>
    </section>
</template>


<script lang="ts">
    import { defineComponent } from 'vue';
    import footerLinks from '../components/FooterLinks.vue';
    import socialMedia from '../components/FooterSocialMedia.vue';
    import gambleAware from '../components/GambleAware.vue';



    export default defineComponent({
        name: 'home',
        components: {
            footerLinks,
            socialMedia,
            gambleAware
        },
    });
</script>


<style scoped>

    section {
        border-top: 4px solid black;
        background-color: #e4002b;
    }

    .container {
        max-width: min(100% - 30px, 1000px);
        display: grid;
        margin-inline: auto;
        padding-block: 1rem;

    }

    small {
        color: white;
    }

    .grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;
    }
</style>