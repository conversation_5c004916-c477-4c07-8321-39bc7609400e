<template>
    <div>

        <div class="container">

            <div class="carousel-container">

                <h2>Join thousands of Imps fans already saving money... </h2>
                <img class="icon icon-1" :src="Icon" />
                <transition :name="transitionName" mode="out-in">
                    <div :key="currentIndex" class="slide">
                        <div class="carousel-image">
                            <img :src="testimonials[currentIndex].image || testimonialPlaceholder"
                                 alt="Testimonial image" />
                        </div>
                        <blockquote>
                            "{{ testimonials[currentIndex].text }}"
                        </blockquote>
                        <p>- {{ testimonials[currentIndex].author }}</p>
                    </div>
                </transition>
                <div v-if="testimonials.length  > 1" class="carousel-controls">
                    <button class="carousel-controls__button btn-previous" @click="previous">&#60;</button>
                    <button class="carousel-controls__button btn-next" @click="next">&#62;</button>
                </div>
                <img class="icon icon-2" :src="Icon" />
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed } from 'vue';



    const testimonial1 = require("../assets/images/TEMPDEV/Imps_TestimonialImage_1.png");
    const testimonial2 = require("../assets/images/TEMPDEV/Imps_TestimonialImage_1.png");
    const testimonial3 = require("../assets/images/TEMPDEV/Imps_TestimonialImage_1.png");
    const testimonials = ref([
        {
            text: "This is great! I got a FREE match ticket for the Lincoln vs West Ham game! - Rob, Rewards4Imps Member",
            author: 'L. Roberts',
            image: testimonial1
        },

        {
            text: "This is great! I got a FREE match ticket for the Lincoln vs West Ham game! - Rob, Rewards4Imps Member",
            author: 'Mike & Liz',
            image: testimonial2
        },

        {
            text: "This is great! I got a FREE match ticket for the Lincoln vs West Ham game! - Rob, Rewards4Imps Member",
            author: 'A. Henderson',
            image: testimonial3
        },

    ]);

    const Icon = require("../assets/images/TEMPDEV/quote svg.svg");

    const currentIndex = ref(0);
    const direction = ref('right'); // Default direction is 'right' for the next button click

    // Transition class name based on the direction
    const transitionName = computed(() => {
        return direction.value === 'left' ? 'fade-left' : 'fade-right';
    });

    const next = () => {
        direction.value = 'left'; // Moving to the next item, slide in from the right
        currentIndex.value = (currentIndex.value + 1) % testimonials.value.length;
    };

    const previous = () => {
        direction.value = 'right'; // Moving to the previous item, slide in from the left
        currentIndex.value = (currentIndex.value - 1 + testimonials.value.length) % testimonials.value.length;
    };
</script>

<style scoped>

    .container {
        padding-top: 1.5rem;
        background: radial-gradient(#e4002b 60%, #b50425);
        z-index: 1;
    }

    .carousel-container {
        position: relative;
        text-align: center;
        max-width: min(100% - 30px, 1100px);
        margin-inline: auto;
        z-index: 2;
    }

    .slide {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        max-width: min(100% - 70px, 600px);
        margin-inline: auto;
        color: #fff;
    }

    .carousel-container h2 {
        color: white;
        font-size: 35px;
        text-align: center;
        line-height: 1;
        max-width: 555px;
        margin-inline: auto;
    }

    .carousel-container span {
        color: #97d7e0;
    }
    /* Slide and fade-in animations */
    .fade-left-enter-active, .fade-right-enter-active,
    .fade-left-leave-active, .fade-right-leave-active {
        transition: opacity 0.5s, transform 0.7s;
    }

    /* When content enters, slide in from left for fade-left */
    .fade-left-enter {
        opacity: 0;
        transform: translateX(-100px); /* Slide from left */
    }

    /* When content leaves, slide out to right for fade-left */
    .fade-left-leave-to {
        opacity: 0;
        transform: translateX(100px); /* Slide to right */
    }

    /* When content enters, slide in from right for fade-right */
    .fade-right-enter {
        opacity: 0;
        transform: translateX(100px); /* Slide from right */
    }

    /* When content leaves, slide out to left for fade-right */
    .fade-right-leave-to {
        opacity: 0;
        transform: translateX(-100px); /* Slide to left */
    }

    blockquote {
        font-size: 18px;
        font-style: italic;
        color: #fff;
        margin-bottom: 10px;
        z-index: 2;
    }

    .carousel-controls__button {
        cursor: pointer;
        background: #0000001c;
        border:none;
        border-radius: 4px;
        color: #fff;
        padding: 15px 15px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    .carousel-image {
   max-width: 250px;
    }

    .btn-previous {
        left: 10px;
    }

    .btn-next {
        right: 10px;
    }


    .icon {
        position: absolute;
        width: 150px;
        height: 150px;
        z-index: -1;
    }

    .icon-1 {
        opacity: 0.1;
        top: 0px;
        left: 0%;
    }


    .icon-2 {
        opacity: 0.1;
        bottom: 0px;
        transform: rotate(180deg);
        right: 0%;
    }


</style>


