<template>
    <div class="masonr-container">
        <div class="masonry">
          
            <div class="card"
                 v-for="(card, index) in cards"
                 :key="index">
                <a :href="card.link">
                    <div class="card-img">
                        <img class="card-img-main" :src="card.mainImage" alt="Main image" />

                    </div>
                    <div v-if="card.text" class="card-text">{{ card.text }}</div>
                    <div v-if="card.price" class="card-price" v-html="card.price">


                    </div>
                </a>
            </div>
           
        </div>
    </div>

</template>


<script setup>
    const logo = require('../assets/images/TEMPDEV/justeatsquare2.png')

    const cards = [
        {
            text: 'Youre saving for Gold Membership',
            price: 'See your Progress >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendGoalTile_GoldMembership_800x400px.png'),
            link: "/spend/my-goal",
        },
        {
            text: 'Spend Your Points on Hospitality',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendTIle_Hospitality_800x400px.png'),
            link: "/spend/save/spend-on",
           
        },
        {
            text: 'Spend your points on Home Match Tickets',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendTile_MatchTickets_800x400px.png'),
            link: "/spend/save/spend-on",
        },
        {
            text: 'Spend Your Points on Memberships',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendTile_Memberships_800x400px.png'),
            link: "/spend/save/spend-on",

        },
        {
            text: 'Spend Your Points on Merchandise',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendTile_Merch_800x400px.png'),
            link: "/spend/save/spend-on",
        },

        {
            text: 'Spend your points and win great prizes',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/SpendTile_WinPrizes_800x400px.png'),
            link: "/spend/save/spend-on",
        },
        {
            text: 'Spend your points to Win Sporting Prizes',
            price: 'Find Out More >>',
            mainImage: require('../assets/images/TEMPDEV/Spend/WSP-SpendTile-820x369-generic.png'),
            link: "/spend/save/spend-on",
        },
    ]
</script>
<style scoped>
    .masonr-container {
        padding-top: 2rem;
    }

    /* Masonry layout */
    .masonry {
        column-count: 3;
        column-gap: 1rem;
    }

    .card {
        display: inline-block;
        position: relative; /* so .card-logo positions relative to each card */
        background: #fff;
        margin-bottom: 1rem;
        overflow: hidden;
        box-shadow: 0 7px 15px -7px #333333ab;
        border: solid 2px #fff;
        width: 100%;
        border-radius: 10px;
    }

    .card-img {
        position: relative; /* logo will be inside this */
        overflow: hidden;
    }

        .card-img img {

        }

    .card-img-main {
        position: relative;
        width: 100%;
        min-height: 100px;
        object-fit: cover;
        border-radius: 10px 10px 0 0;
        filter: brightness(0.8) contrast(1.1);
        transition: filter 0.3s ease-in-out, transform 0.3s ease-in-out;
        object-position:center;
    }

        .card-img-main::after {
            content: "";
            position: absolute;
            top: 0;
            border-radius: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background: linear-gradient(180deg, rgba(252, 176, 69, 0) 0%, #0d0d0d 69%);
            opacity: 0.7;
            pointer-events: none;
            transform: scale(0.995);
        }


    .card-logo {
        position: absolute;
        bottom: -25px; /* overlap slightly */
        left: 1rem;
        background: #fff;
        box-shadow: 0 7px 15px -7px rgba(0,0,0,0.82);
        border: solid 2px #ecf0f3;
        max-width: 80px;
    }

        .card-logo img {
            width: 100%;
            display: block;
          
        }

    .card-text {
        color: black;
        padding: 1rem 1rem;
        font-size: 25px;
        line-height: 1;
    }

    a {
        text-decoration: none;
    }
        a:hover .card-price {
            text-decoration: underline;
        }

        a:hover .card-img-main {
            filter: brightness(1) contrast(1);
            transform: scale(1.04);
        }

    .card-price {
        padding: 0.5rem 1rem;
        color: black;
        font-weight: 600;
        font-size: 1rem;
        text-align: end;
    }

    /* Responsive tweaks */
    @media (max-width: 768px) {
        .masonry {
            column-count: 2;
        }
    }

    @media (max-width: 480px) {
        .masonry {
            column-count: 1;
        }
    }
</style>
