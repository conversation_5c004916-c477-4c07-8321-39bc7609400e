<template>
  <div class="cards-container">
      <div class="masonry">
          <a href="/collect">
              <div class="card"
                   v-for="(card, index) in cards"
                   :key="index">
                  <div class="card-img">
                      <img class="card-img-main" :src="card.mainImage" alt="Main image" />
                      <div class="card-logo">
                          <img :src="card.logoImage" alt="Logo" />
                      </div>
                  </div>
                  <div class="card-text">{{ card.text }}</div>
                  <div class="card-price" v-html="card.price"></div>

              </div>
          </a>
      </div>
  </div>
</template>

<script setup>
const logo = require('../assets/images/TEMPDEV/justeatsquare2.png')

const cards = [
  {
        text: 'Collect points when you shop online with Boots',
    price: 'Up to 12 point per &pound; >>',
    mainImage: require('../assets/images/TEMPDEV/boots-new-header-jun25.jpg'),
        logoImage: require('../assets/images/TEMPDEV/square_logobootsnew.png'),
  },
  {
      text: 'Collect points when you shop online with Boots',
      price: 'Earn 2 points per &pound; >>',
      mainImage: require('../assets/images/TEMPDEV/marksandspencer_merchant_header_banner_01122023-2.jpg'),
      logoImage: require('../assets/images/TEMPDEV/marksandspencersquare2207.png'),
  },
  {
      text: 'Collect points when you shop online with Boots',
      price: 'Earn 3 points per &pound; >>',
      mainImage: require('../assets/images/TEMPDEV/moonpigbanner.jpg'),
      logoImage: require('../assets/images/TEMPDEV/moonpig-square.png'),
  },
  {
      text: 'Collect points when you shop online with Boots',
      price: 'Earn 4 points per &pound; >>',
      mainImage: require('../assets/images/TEMPDEV/just-eat-new-header-nov24.jpg'),
      logoImage: require('../assets/images/TEMPDEV/justeatsquare2.png'),
  },
  {
      text: 'Collect points when you shop online with Boots',
      price: 'Earn 5 points per &pound; >>',
      mainImage: require('../assets/images/TEMPDEV/verymerchantbannerv2030425.png'),
      logoImage: require('../assets/images/TEMPDEV/verylogosq.png'),
  },
  {
      text: 'Collect points when you shop online with Boots',
      price: 'Earn bonus points >>',
      mainImage: require('../assets/images/TEMPDEV/dunelm_banner_2025_summer.jpg'),
      logoImage: require('../assets/images/TEMPDEV/dunelm-sq.png'),
  }
]
</script>
<style scoped>
    .cards-container {
        position: relative;
        max-width: min(100% - 30px, 1000px);
        margin-inline: auto;
        z-index: 2;
    }

    /* Masonry layout */
    .masonry {
        column-count: 3;
        column-gap: 1rem;
    }

    .card {
        display: inline-block;
        position: relative; /* so .card-logo positions relative to each card */
        background: #fff;
        margin-bottom: 1rem;
        overflow: hidden;
        box-shadow: 0 7px 15px -7px #333333ab;
        border: solid 2px #fff;
        width: 100%;
        border-radius: 10px;
      
     
    }

    .card-img {
        position: relative; /* logo will be inside this */
      
    }

        .card-img img {
        
        }

    .card-img-main {
        position: relative;
        width: 100%;
        aspect-ratio: 3 / 1;
        object-fit: cover;
        filter: contrast(1.2);
        border-radius: 10px 10px 0 0;
        filter: brightness(0.8) contrast(1.1);
    }

        .card-img-main::after {
            content: "";
            position: absolute;
            top: 0;
            border-radius: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background: linear-gradient(180deg, rgba(252, 176, 69, 0) 0%, #0d0d0d 69%);
            opacity: 0.7;
            pointer-events: none;
            transform: scale(0.995);
        }


    .card-logo {
        position: absolute;
        bottom: -25px; /* overlap slightly */
        left: 1rem;
 
        border-radius: 10px;
        box-shadow: 0 7px 15px -7px #000000d1;
        border: solid 2px #ecf0f3;
   
  
        max-width: 80px;
    }

        .card-logo img {
            width: 100%;
            display: block;
            border-radius: 8px;
        }

    .card-text {
        padding: 2.5rem 1rem 0.5rem; /* extra padding for logo space */
        font-size: 1rem;
        color: #1a1d19;
    }

    .card-price {
        padding: 0 1rem 1rem;
        color: black;
        font-weight:600;
        font-size: 1rem;
        text-decoration: none;
    
    }

    /* Responsive tweaks */
    @media (max-width: 768px) {
        .masonry {
            column-count: 2;
        }
    }

    @media (max-width: 480px) {
        .masonry {
            column-count: 1;
        }
    }
</style>
