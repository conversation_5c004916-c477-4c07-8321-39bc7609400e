import { Tenants } from "../constants/Tenants";

export class TenantService {
  private static instance: TenantService;
  private currentTenantName: string | null = null;

  private constructor() {}

  public static getInstance(): TenantService {
    if (!TenantService.instance) {
      TenantService.instance = new TenantService();
    }
    return TenantService.instance;
  }

  public getTenantName(): string {
    if (!this.currentTenantName) {
      this.currentTenantName = this.resolveTenantFromEnvironment();
    }
    return this.currentTenantName;
  }

  private resolveTenantFromEnvironment(): string {
    const hostname = window.location.hostname.toLowerCase();
    let tenantName: string | undefined;

    // for local dev
    if (process.env.NODE_ENV === "development" || hostname === "localhost") {
      const envTenant = process.env.VUE_APP_TENANT_NAME;
      if (envTenant) {
        tenantName = envTenant;
        return tenantName;
      }
    }
    
    // for deployed version
    if (
      hostname.includes(".ie") ||
      hostname.includes("irish") ||
      hostname.includes("-ie")
    ) {
      tenantName = Tenants.Rewards4RacingIreland;
      return tenantName;
    }

    if (hostname.includes("racing")) {
      tenantName = Tenants.Rewards4Racing;
      return tenantName;
    }    

    if (hostname.includes("imps")) {
      tenantName = Tenants.Rewards4Imps;
      return tenantName;
    }

    console.warn(`[Tenant] Could not resolve tenant from hostname: ${hostname}, using default`);
    return '';
  }
}

export default TenantService.getInstance();
