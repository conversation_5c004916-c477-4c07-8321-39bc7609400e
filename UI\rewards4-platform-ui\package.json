{"name": "rewards4-platform-ui-vue", "version": "0.0.0", "description": "rewards4-platform-ui-vue", "main": "server.js", "author": {"name": ""}, "scripts": {"start": "ts-node --project tsconfig.server.json server.ts", "serve": "vue-cli-service serve", "typecheck": "tsc -p tsconfig.app.json --noEmit", "build": "vue-cli-service build"}, "dependencies": {"@vue/cli-plugin-babel": "~5.0.0", "axios": "^1.7.1", "core-js": "^3.8.3", "pinia": "^3.0.3", "sweetalert2": "^11.22.5", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@types/node": "^24.3.0", "@typescript-eslint/eslint-plugin": "^5.33.0", "@typescript-eslint/parser": "^5.33.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^8.21.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "^5.9.2"}, "eslintConfig": {"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"]}}