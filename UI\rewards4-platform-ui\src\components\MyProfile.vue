<template>
    <div class="my-profile">
          <div class="container">
    <div class="card" role="region" aria-labelledby="editProfileHeading">
 

        <form id="profileForm" novalidate>


            <div class="field">
                <label for="memberId">Member ID</label>
                <input id="memberId" name="memberId" disabled type="text" readonly placeholder="123456" aria-readonly="true">

            </div>

            <div class="field">
                <label for="email">Email address</label>
                <input id="email" name="email" type="email" disabled autocomplete="email" required placeholder="<EMAIL>">
                <span class="error" id="err-email" aria-live="polite"> To change your email address, 'please contact our Member Services team'</span>
            </div>

            <div class="field">
                <label for="firstName">First name</label>
                <input id="firstName" name="firstName" type="text" autocomplete="given-name" value="Liam" required maxlength="60" placeholder="e.g. <PERSON>">
                <span class="error" id="err-firstName" aria-live="polite"></span>
            </div>

            <div class="field">
                <label for="surname">Surname</label>
                <input id="surname" name="surname" type="text" autocomplete="family-name" required value="Gallagher" maxlength="60" placeholder="e.g. Smith">
                <span class="error" id="err-surname" aria-live="polite"></span>
            </div>


            <div class="field">
                <label for="dob">Date of birth</label>
                <input id="dob" name="dob" value="1990-01-01" type="date" required aria-describedby="dobHelp">


                <span class="error" id="err-dob" aria-live="polite"></span>
            </div>

            <div class="field">
                <label for="mobile">Mobile number</label>
                <input id="mobile" name="mobile" type="tel" inputmode="tel" value="07123123123" required placeholder="e.g. +44 7700 900123" maxlength="20">
                <span class="error" id="err-mobile" aria-live="polite"></span>
            </div>

            <div class="field full">
                <label for="address1">First line of address</label>
                <input id="address1" name="address1" type="text" value="10 Downing Street" required maxlength="120" placeholder="e.g. 10 Downing Street">
                <span class="error" id="err-address1" aria-live="polite"></span>
            </div>

            <div class="field">
                <label for="postcode">Postcode</label>
                <input id="postcode" name="postcode" type="text" value=" SW1A 2AA" required maxlength="10" placeholder="e.g. SW1A 2AA" autocomplete="postal-code">
                <span class="error" id="err-postcode" aria-live="polite"></span>
            </div>
            <div></div>
            <div class="actions full">
                <button type="button" class="btn primary" id="updateBtn" @click="goToChangePassword">
                    Reset Password
                </button>

                <button type="submit" class="btn primary" id="saveBtn">
                    Update Details
                </button>
            </div>
        </form>

      <div id="status" role="status" aria-live="polite" style="margin-top:12px"></div>
    </div>
  </div>
    </div>
</template>

<script setup lang="ts">
    import { useRouter } from 'vue-router'
    const router = useRouter()

    function goToChangePassword(): void {
        router.push('/change-password')
    }
</script>


<style scoped>

 
    form {
        display: grid;
        gap:1.3rem;
        grid-template-columns: 1fr 1fr;
    }

    .field {
        display: grid;
    }

        .field label {
            font-weight: 600;
        }

        input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            color: black;
        }

    .actions {
        justify-self: end;
        width: 100%;
        display: flex;
        gap:1rem;
    }

    .btn {
        width: 100%;
        padding: 5px 5px;
        border: 2px solid #7b786538;
        background-color: black;
        border-radius: 5px;
        color: white;
        min-height: 65px;
        cursor: pointer;
        font-size: 18px;
    }

    span {
        height: 0px;
        font-size: 12px;
        text-wrap: nowrap;
    }


    @media (max-width: 768px) {
        form {
            grid-template-columns: 1fr;
        }
        span {
            height:auto;
            font-size: 12px;
            text-wrap: wrap;
        }
    }


    @media (max-width: 385px) {
        .actions {
         flex-wrap:wrap;
        }
    }
</style>