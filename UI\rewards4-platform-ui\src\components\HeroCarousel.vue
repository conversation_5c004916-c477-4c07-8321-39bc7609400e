<template>
    <div class="carousel"
         @touchstart="handleTouchStart"
         @touchend="handleTouchEnd">
        <transition name="fade-slide" mode="out-in">
            <div class="carousel-item" :key="currentIndex">
                <picture>
                    <!-- Desktop image -->
                    <source :srcset="slides[currentIndex].desktop" media="(min-width: 600px)" />
                    <!-- Mobile fallback -->
                    <img :src="slides[currentIndex].mobile"
                         alt="carousel image"
                         class="mx-auto" />
                </picture>
            </div>
        </transition>

        <!--<button class="carousel-control prev" @click="prevSlide">&#10094;</button>
        <button class="carousel-control next" @click="nextSlide">&#10095;</button>-->

        <div class="carousel-indicators">
            <span v-for="(slide, index) in slides"
                  :key="index"
                  :class="{ active: index === currentIndex }"
                  @click="goToSlide(index)"></span>
        </div>
    </div>
</template>
<img src="../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Mobile_650x800.png" />

<script setup>
    import { ref, onMounted, onBeforeUnmount } from 'vue'

    const slides = [
        {
            desktop: require('../assets/images/TEMPDEV/Lincoln_Collect_Retailers_1000x420.png'),
            mobile: require('../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Mobile_650x800.png')
        },
        {
            desktop: require('../assets/images/TEMPDEV/Lincoln_RAF_DesktopCarousel_1000x420.png'),
            mobile: require('../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Mobile_650x800.png')
        },
        {
            desktop: require('../assets/images/TEMPDEV/Lincoln_CollectPoints_ClubPartner_DesktopCarousel.png'),
            mobile: require('../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Mobile_650x800.png')
        },
        {
            desktop: require('../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Desktop_1000x420.png'),
            mobile: require('../assets/images/TEMPDEV/Lincoln_SpecialOffers_Carousel_Mobile_650x800.png')
        }
    ]

    const currentIndex = ref(0)
    let interval = null


    function startAutoSlide() {
        interval = setInterval(nextSlide, 10000)
    }

    function stopAutoSlide() {
        if (interval) clearInterval(interval)
    }

    function nextSlide() {
        currentIndex.value = (currentIndex.value + 1) % slides.length
    }

    function prevSlide() {
        currentIndex.value = (currentIndex.value - 1 + slides.length) % slides.length
    }

    function goToSlide(index) {
        currentIndex.value = index
    }

    // Swipe support
    let startX = 0

    function handleTouchStart(e) {
        startX = e.changedTouches[0].clientX
        stopAutoSlide()
    }

    function handleTouchEnd(e) {
        const endX = e.changedTouches[0].clientX
        const diff = endX - startX

        if (diff > 50) {
            prevSlide()
        } else if (diff < -50) {
            nextSlide()
        }

        startAutoSlide()
    }

    onMounted(() => {
        startAutoSlide()
    })

    onBeforeUnmount(() => {
        stopAutoSlide()
    })
</script>

<style scoped>
    .carousel {
        position: relative;
        width: 100%;
        max-width: 1000px;
        aspect-ratio: 2.4 / 1;
        margin: auto;
        overflow: hidden;
    }

    .carousel-item img {
        width: 100%;
        display: block;
        max-height: fit-content;
    }

    /* Improved fade + slight slide transition */
    .fade-slide-enter-active,
    .fade-slide-leave-active {
        transition: opacity 0.5s ease;
    }

    .fade-slide-enter-from {
        opacity: 0;
    }

    .fade-slide-leave-to {
        opacity: 0;
    }

    .carousel-control {
        position: absolute;
        top: 50%;
        background: rgba(0,0,0,0.5);
        border: none;
        color: white;
        font-size: 2rem;
        transform: translateY(-50%);
        cursor: pointer;
        z-index: 10;
    }

        .carousel-control.prev {
            left: 10px;
        }

        .carousel-control.next {
            right: 10px;
        }

    .carousel-indicators {
        position: absolute;
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 20;
    }

        .carousel-indicators span {
            display: inline-block;
            height: 17px;
            width: 17px;
            margin: 0 4px;
            background: rgba(255,255,255,0.7);
            border-radius: 50%;
            border: 1px solid black;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

            .carousel-indicators span.active {
                background: #fff;
                transform: scale(1.3);
            }

            @media (max-width: 600px) {
                .carousel {
                    aspect-ratio: 2/2.4;
                }
            }
</style>
