<template>
    <section>

        <div class="container">
            <masonrySpendCards></masonrySpendCards>
        </div>
     

    </section>
        </template>


<script lang="ts">
    import { defineComponent } from 'vue';
    import masonrySpendCards from '../components/MasonrySpendCards.vue';
 


    export default defineComponent({
        name: 'home',
        components: {
        
            masonrySpendCards
        },
    });
</script>

<style scoped >
    section {
        background-color: #f3f3f3;
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .container {
        max-width: min(100% - 30px, 1000px);
        margin-inline: auto;
    }
</style>