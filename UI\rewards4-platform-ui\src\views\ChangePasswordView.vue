<template>
    <section>
        <div class="bg-img">
            <div v-if="!submitted" class="forgot-password-container">
                <div class="password-link-section">
                    <h1>Set a new password</h1>

                    <p class="text">
                        To change your account password please fill in the fields below
                    </p>

                    <form @submit.prevent="submitEmail" id="forgotPasswordForm">
                        <input type="email"
                               v-model="email"
                               placeholder="Currecnt Password"
                               pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                               minlength="7"
                               maxlength="320"
                                />
                        <input type="email"
                               v-model="email"
                               placeholder="New Password"
                               pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                               minlength="7"
                               maxlength="320"
                                />
                        <input type="email"
                               v-model="email"
                               placeholder="Confirm"
                               pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
                               minlength="7"
                               maxlength="320"
                                />

                        <button type="submit" :disabled="loading">
                            <span v-if="!loading">Send Reset Link</span>
                            <span v-else>Sending...</span>
                        </button>
                    </form>
                </div>
            </div>

            <div v-else class="forgot-password-finish">
                <div class="password-link-section">
                    <img class="tick-icon" src="../assets/images/TEMPDEV/circle-check-regular-full.svg" />
                    <h1>Password Updated!</h1>
                    <p>
                        Your password has been changed successfully. You can now use your new password to log in.
                    </p>
                </div>
                </div>
            </div>
    </section>
</template>

<script setup>
    import { ref } from 'vue'

    const email = ref('')
    const loading = ref(false)
    const submitted = ref(false)

    async function submitEmail() {
        loading.value = true

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500))

            // In a real app, you'd send `email.value` to your API here
            submitted.value = true
        } catch (err) {
            console.error('Error sending reset link:', err)
        } finally {
            loading.value = false
        }
    }
</script>

<style scoped>

    section {
        background-image: url('https://images.unsplash.com/photo-1641029185333-7ed62a19d5f0?crop=entropy&cs=srgb&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NTQ0ODg1OTZ8&ixlib=rb-4.1.0&q=85');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .bg-img {
        backdrop-filter: brightness(0.6) contrast(1.1) saturate(1.5);
        padding-block: 2rem;
        min-height: calc(100vh - 300px);
        align-content: center;
    }

    .password-link-section {
        max-width:390px;
        margin-inline: auto;
    }

    .forgot-password-container {
        max-width: min(100% - 30px, 450px);
        display: grid;
        background: #ffffffe0;
        box-shadow: 0.063em 0.75em 1.563em rgba(0, 0, 0, 0.78);
        margin-inline: auto;
        padding-block: 3rem;
        border-radius: 10px;
        border: 2px solid white;
    }

    .forgot-password-finish {
        max-width: min(100% - 30px, 450px);
        display: grid;
        background: #ffffffe0;
        box-shadow: 0.063em 0.75em 1.563em rgba(0, 0, 0, 0.78);
        margin-inline: auto;
        padding-block: 3rem;
        border-radius: 10px;
        gap: 1rem;
        border: 2px solid white;
        text-align: center;
     
        justify-items: center;
    }

    @media (max-width: 420px) {
        .forgot-password-container {
            padding: 1rem;
        }
    }

    .icon {
        height: 50px;
        width: 50px;
        margin-inline: auto;
        margin-top: 2rem;
    }

    h1 {
        font-size: 27px;
        color: var(--clr-black-90);
        text-align: center;
        line-height: 1;
        padding-bottom: 0.2rem;
    }

    .text {
        color: var(--clr-black-90);
        font-size: 14px;
        text-align: center;
    }

    form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding-top: 1rem;
    }

    input[type="email"] {
        width: 100%;
        padding: 15px;
        font-size: 18px;
        border: 1px solid #ccc;
        border-radius: 5px;
        box-sizing: border-box;
        color: black;
       
        background-color: white;
    }

        input[type="email"]:focus {
            outline: 1px solid var(--clr-primary);
        }

    input::placeholder {
        font-size: 18px;
    }

    button {
        width: 100%;
        padding: 5px 5px;
        background: black;
        border-radius: 5px;
        color: white;
        border: none;
        min-height: 67px;
        cursor: pointer;
        font-size: 20px;
        text-transform: uppercase;
    }

    .message {
        font-size: 14px;
        color: var(--clr-primary);
        margin-top: 10px;
    }

    .error {
        font-size: 14px;
        color: var(--clr-primary);
        margin-top: 10px;
    }

    .tick-icon {
        height: 70px;
        text-align: center;
        justify-self: center;
    }
</style>
