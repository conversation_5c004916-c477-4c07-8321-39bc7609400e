<template>
    <div>
        <h1>Follow our simple steps...</h1>
        <!-- Mobile carousel -->
        <div v-if="isMobile"
             class="carousel"
             @touchstart="handleTouchStart"
             @touchend="handleTouchEnd">
            <transition name="fade-slide" mode="out-in">
                <div class="carousel-item" :key="currentIndex">
                    <div class="step">
                        <img :src="slides[currentIndex].mobile" alt="Slide image" />
                        <div class="step-title">{{ slides[currentIndex].title }}</div>
                        <div class="step-text">{{ slides[currentIndex].text }}</div>
                    </div>
                </div>
            </transition>

            <!-- Indicators (now visible + clickable) -->
            <div class="carousel-indicators" role="tablist" aria-label="Slide indicators">
                <button v-for="(slide, index) in slides"
                        :key="index"
                        type="button"
                        :class="{ active: index === currentIndex }"
                        :aria-current="index === currentIndex ? 'true' : 'false'"
                        :aria-label="`Go to slide ${index + 1}`"
                        @click="goToSlide(index)" />
            </div>
        </div>


        <!-- Desktop / larger screens -->
        <div v-else class="slides-flex">
            <div v-for="(slide, index) in slides" :key="index" class="step">
                <img :src="slide.mobile" alt="Slide image" />
                <div class="step-title">{{ slide.title }}</div>
                <div class="step-text">{{ slide.text }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, onMounted, onBeforeUnmount } from "vue";

    const slides = [
        {
            mobile: require('../assets/images/TEMPDEV/Lincoln_HowItWorks_Click (1).png'),
            title: "1. Browse",
            text: "Find your favorites among 1,000+ brands",
        },
        {
            mobile: require('../assets/images/TEMPDEV/Lincoln-3-shoponline.png'),
            title: "2. Click",
            text: "Click through from our site and complete your transaction as normal",
        },
        {
            mobile: require('../assets/images/TEMPDEV/Lincoln_HowItWorks_Click (1).png'),
            title: "3. Collect",
            text: "Your points will be added once your transaction is confirmed",
        },
    ];

    const currentIndex = ref(0);
    let interval = null;
    const isMobile = ref(window.innerWidth < 650);

    function handleResize() {
        isMobile.value = window.innerWidth < 650;
        if (!isMobile.value) stopAutoSlide();
        else startAutoSlide();
    }

    // Carousel logic
    function startAutoSlide() {
        stopAutoSlide();
        interval = setInterval(nextSlide, 10000);
    }

    function stopAutoSlide() {
        if (interval) clearInterval(interval);
    }

    function nextSlide() {
        currentIndex.value = (currentIndex.value + 1) % slides.length;
    }

    function prevSlide() {
        currentIndex.value = (currentIndex.value - 1 + slides.length) % slides.length;
    }

    function goToSlide(index) {
        currentIndex.value = index;
    }

    // Swipe support
    let startX = 0;
    function handleTouchStart(e) {
        startX = e.changedTouches[0].clientX;
        stopAutoSlide();
    }

    function handleTouchEnd(e) {
        const endX = e.changedTouches[0].clientX;
        const diff = endX - startX;

        if (diff > 50) {
            prevSlide();
        } else if (diff < -50) {
            nextSlide();
        }

        startAutoSlide();
    }

    onMounted(() => {
        window.addEventListener("resize", handleResize);
        if (isMobile.value) startAutoSlide();
    });

    onBeforeUnmount(() => {
        window.removeEventListener("resize", handleResize);
        stopAutoSlide();
    });
</script>

<style scoped>
    .slides-flex {
        display: flex;
        gap: 20px;
        justify-content: center;
        align-items: flex-start;
        flex-wrap: wrap;
    }

    .step {
        max-width: 200px;
        text-align: center;
        justify-self: center;
    }


    h1 {
        line-height: 1;
        text-align: center;
        padding-top: 2rem;
   
        padding-bottom: 1rem;
        font-size: 34px;
        color: #4c4c4c;
        font-weight: 600;
    }

    .step-title {
        text-align: center;
        font-size: 28px;
        color: #4c4c4c;
        font-weight: 600;
    }

    .step-text {
        text-align: center;
    }

    /* Improved fade + slight slide transition */
    .fade-slide-enter-active,
    .fade-slide-leave-active {
        transition: opacity 0.5s ease;
    }

    .fade-slide-enter-from {
        opacity: 0;
    }

    .fade-slide-leave-to {
        opacity: 0;
    }


    /* Carousel base */
    .carousel {
        position: relative; /* ensures indicators can be absolutely positioned */
        overflow: hidden;
        padding-bottom: 2rem;
    
    }

    .carousel-item img {
        display: block;
        width: 100%;
        height: auto;
    }


    /* Indicators */
    .carousel-indicators {
        position: absolute;
        left: 50%;
        bottom: 0px;
        transform: translateX(-50%);
        background: rgba(255,255,255,0.7);
        display: flex;
        gap: 8px;
        z-index: 10; /* above the image */
        pointer-events: auto;
    }

        .carousel-indicators button {
            display: inline-block;
            height: 17px;
            width: 17px;
            
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            border: 1px solid black;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin-bottom:0.5rem;
        }

            .carousel-indicators button.active {
                background: #4c4c4c21;
                transform: scale(1.3);
            }

    /* Hide indicators on ?650px since carousel is off */
    @media (min-width: 650px) {
        .carousel-indicators {
            display: none;
        }
    }

</style>























<!--<template>
    <div>
        <h1>Follow our simple steps...</h1>
        <div class="flex">

            <div class="step">
                <img src="../assets/images/TEMPDEV/Lincoln-3-shoponline.png" />
                <div class="step-title">1. Browse</div>
                <div class="step-text">
                    Check out our 1,000+ brands
                </div>
            </div>
            <div class="step">
                <img src="../assets/images/TEMPDEV/Lincoln-3-shoponline.png" />
                <div class="step-title">2. Click</div>
                <div class="step-text">
                    Click through from our site and complete your transaction as normal
                </div>
            </div>
            <div class="step">
                <img src="../assets/images/TEMPDEV/Lincoln_HowItWorks_Click (1).png" />
                <div class="step-title">3. Collect</div>
                <div class="step-text">
                    Your points will be added one your transaction is confirmed
                </div>
            </div>
        </div>



    </div>

</template>


<style scoped>
    img {
    }


    .step {
        width: 200px;
    }

    h1 {
        line-height: 1;
        text-align: center;
        padding-top: 2rem;
        padding-bottom: 0.5rem;
        font-size: 34px;
        color: #4c4c4c;
        font-weight: 600;
    }

    .flex {
        display: flex;
        justify-content: center;
        gap: 2rem;
        max-width: 700px;
        margin-inline: auto;
        flex-wrap: wrap;
    }

    .step-title {
        text-align: center;
        font-size: 28px;
        color: #4c4c4c;
        font-weight: 600;
    }

    .step-text {
        text-align: center;
    }
</style>-->
