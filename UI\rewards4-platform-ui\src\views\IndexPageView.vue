<template>
    <hero></hero>
    <SpendSave></SpendSave>
    <collect></collect>
    <brands :retailerBrandImages="pageContent?.retailerBrandImages"></brands>
    <Spend></Spend>
    <testimonial></testimonial>
    <cta></cta>
</template>


<script lang="ts">
    import { defineComponent, ref } from 'vue';
    import hero from '../components/IndexPageHero.vue';
    import Spend from '../components/IndexPageSpend.vue';
    import collect from '../components/IndexPageCollect.vue';
    import brands from '../components/IndexPageBrands.vue';
    import testimonial from '../components/Carousel.vue';
    import cta from '../components/CTA.vue';
    import pageService from '../services/page-service';
    import IndexPageDataResponse from '../responses/IndexPageDataResponse';    


    export default defineComponent({
        name: 'index',
        components: {
            hero,
            Spend,
            collect,
            brands,
            testimonial,
            cta
        },
        async setup() {            
            const pageContent = ref<IndexPageDataResponse>();
            await pageService.getIndexPageContent().then(response => {
                pageContent.value = response;
            });

            return { pageContent };
        }
    });
</script>