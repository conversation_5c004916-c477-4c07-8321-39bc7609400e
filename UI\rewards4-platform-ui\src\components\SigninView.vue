<template>
  <nav>
    <router-link to="/">Sign Up</router-link> |
    <router-link to="/signin">Sign In</router-link>
  </nav>

  <div class="signin">
    <form @submit.prevent="submitForm">
      <h1>Signin</h1>
    
      <label>Email</label>
      <input type="email" placeholder="Enter your email name" v-model="signinRequest.emailAddress" required>
      <label>Password</label>
      <input type="password" placeholder="Enter your password" v-model="signinRequest.password" required>
      <br/>
      <button type="submit">Signin</button>
   </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import SigninRequest from '../requests/SigninRequest'

const router = useRouter()
const authStore = useAuthStore()

const signinRequest = ref<SigninRequest>({
  emailAddress: '',
  password: ''
})

const submitForm = async () => {
  try {
    const response = await authStore.signin(signinRequest.value)
    
    if (response?.isSuccess && authStore.isAuthenticated) {
      router.push('/home')
    }
  } catch (error) {
    console.error(error)
  }
}
</script>

<style scoped>
form {
    max-width: 420px;
    margin: 30px auto;
    background: crimson;
    text-align: center;
    padding: 40px;
    border-radius: 10px;
  }
  label {
    color: #aaa;
    display: flex;
    margin: 25px 0 15px;
    font-size: 0.7em;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
  }
  input {
    display: block;
    padding: 10px 6px;
    width: 100%;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #ddd;
    color: #555;
    border-radius: 5px;
  }
  button {
    display: block;
    padding: 10px 6px;
    width: 100%;
    box-sizing: border-box;
    border: none;
    border-bottom: 1px solid #ddd;
    color: #555;
    border-radius: 5px;
    font-weight: bold;
  }
</style>
