import { defineStore } from 'pinia';
import { ref } from 'vue';
import AuthenticationService from '../services/authentication-service';
import SignupRequest from '../requests/SingupRequest';
import SigninRequest from '../requests/SigninRequest';
import SigninResponse from '../responses/SigninResponse';

export const useAuthStore = defineStore('auth', () => {
  const isAuthenticated = ref<boolean>(false)
  const member = ref<{
    memberId?: string
  }>({})

  const signup = async (signupRequest: SignupRequest) => {
    try {
      const response = await AuthenticationService.signup(signupRequest)
      if (response?.isSuccess === true) {        
        isAuthenticated.value = true             
      }      
      return response
    } catch (error) {
      throw error;
    }
  }

  const signin = async (signinRequest: SigninRequest): Promise<SigninResponse> => {
    try {
      const response = await AuthenticationService.signin(signinRequest)     
      
      if (response?.isSuccess === true) {        
        isAuthenticated.value = true
        member.value = {
          memberId: response.memberId.toString()
        }        
      }      
      return response
    } catch (error) {
      throw error
    }
  }

  const signout = async () => {
    try {
      await AuthenticationService.signout()
      isAuthenticated.value = false
      member.value = {}
    } catch (error) {
      isAuthenticated.value = false
      member.value = {}
    }
  }

  return {
    isAuthenticated,
    member,
    signup,
    signin,
    signout
  }
})









