<template>
 
 
        <h2>Terms & Conditions</h2>
        <div class="accordion">
            <div class="accordion-item">
                <button id="accordion-button-1" aria-expanded="false"><span class="accordion-title">Terms</span><span class="icon" aria-hidden="true"></span></button>
                <div class="accordion-content">
                    <h3>Why should I trust Win Sporting Prizes?</h3>

                    <p>
                        Win Sporting Prizes is proudly operated by Rewards4, a trusted name in loyalty, rewards and competitions for over 14 years. With a strong track record of delivering exceptional experiences through platforms like Rewards4Racing, Rewards4Golf, Wolves Rewards, Affordable Golf Rewards, and Tigers ROARwards, we're committed to providing a secure and reliable competition platform that sports fans can trust.
                    </p>
                    <h3>Where are you based?</h3>
                    <p>Our head office is based in Burton-on-Trent in the Midlands and our IT team is based in our Manchester office. </p>
                </div>
            </div>
            <div class="accordion-item">
                <button id="accordion-button-2" aria-expanded="false"><span class="accordion-title"> Exclusions</span><span class="icon" aria-hidden="true"></span></button>
                <div class="accordion-content">
                    <h3>How do you pick a winner and why don't you do live draws?</h3>

                    <p>We use a trusted third-party company (RANDOM.ORG) to randomly select our winners. This ensures complete fairness, transparency, and security. The draw process is fully auditable, and all data is handled securely, giving all participants confidence in the integrity of our competitions.</p>

                    <p>We don't conduct live draws because this would require us to select a winner in-house, which isn't best practice. Using an independent third party eliminates any potential bias and reinforces trust in the process.</p>

                    <p>Additionally, before we announce a winner, we must verify their eligibility to ensure they meet all entry requirements. This includes confirming their compliance with the competition rules, which wouldn't be possible in a live draw scenario.</p>

                    <p>
                        By following this process, we guarantee a fair, compliant, and transparent experience for all participants.
                    </p>

                    <h3>How exactly is a winner picked?</h3>

                    <p>
                        We use RANDOM.ORG, a trusted third-party service that generates true random results using atmospheric noise rather than computer algorithms (such as Google Random Number Generator). This ensures complete fairness, security, and transparency in our draws.
                    </p>

                    <p>Here's how the process works:</p>

                    <ul>
                        <li>Entry List Submission - We provide RANDOM.ORG with a list of all eligible entries.</li>
                        <li>Random Draw Process - The system uses true random number generation (RNG) to select a winner completely at random.</li>
                        <li>Security & Auditability - The draw results are logged and can be independently verified, ensuring full transparency.</li>
                        <li>
                            Winner Verification - Once a winner is drawn, we verify their eligibility before officially announcing them.
                        </li>
                    </ul>

                    <p>By using RANDOM.ORG, we guarantee an unbiased, independently verifiable process that ensures every participant has a fair chance of winning.</p>

                    <h3>What happens if a winner isn't verified or doesn't accept the prize?</h3>

                    <p>If a winner fails to respond within the specified timeframe, is unable to verify their identity, or does not accept the prize, we will select an alternate winner using the same random selection process.</p>

                    <p>Our third-party winner selection service, RANDOM.ORG, securely records all draw data, including the ticket number of the original winner. This ensures a full audit trail is available, confirming the integrity and fairness of the selection process.</p>

                    <p>If a redraw is required, it will follow the exact same procedure as the initial draw, with RANDOM.ORG selecting a new winner at random.</p>

                    <h3>When and where are winners announced?</h3>

                    <p>Once the winner has been verified, their name and prize details will be announced on the Win Sporting Prizes website and our official social media pages, including Facebook and Instagram.</p>

                </div>
            </div>
        

       
       
        </div>
  
</template>

<script lang="ts">
    import { defineComponent, inject, onMounted, ref } from "vue";
    import { useRouter } from "vue-router";

    export default defineComponent({
        name: "Faqs",
        setup() {
            const router = useRouter();
           
         

            onMounted(() => {
               

                // Accordion functionality
                const items = document.querySelectorAll(".accordion button");
                function toggleAccordion(this: HTMLButtonElement) {
                    const itemToggle = this.getAttribute('aria-expanded');

                    items.forEach(item => item.setAttribute('aria-expanded', 'false'));

                    if (itemToggle === 'false') {
                        this.setAttribute('aria-expanded', 'true');
                    }
                }

                items.forEach(item => item.addEventListener('click', toggleAccordion));
            });

            
        }
    });
</script>


<style scoped>
    .accordion {
        padding-bottom: 3rem;
     
    }

        .accordion .accordion-item {
            border-bottom: 1px solid lightgray;
            max-width: min(100% - 50px, 595px);
            margin-inline: auto;
        }

        .accordion .accordion-item button[aria-expanded='true'] {
            border-bottom: 1px solid var(--clr-primary);
        
        }

    button {
        position: relative;
        display: block;
        text-align: left;
        width: 100%;
        padding: 1em 0;
        color: var(--clr-black-90);
        font-size: 1.15rem;
        font-weight: 400;
        border: none;
        background: none;
        outline: none;
    }

        button:hover, button:focus {
            cursor: pointer;
            color: var(--clr-primary);
        }

            button:hover::after, button:focus::after {
                cursor: pointer;
                color: var(--clr-primary);
                border: 1px solid var(--clr-primary);
            }

    .accordion-title {
        padding: 1em 1.5em 1em 0;
    }

    .icon {
        display: inline-block;
        position: absolute;
        top: 18px;
        right: 0;
        width: 22px;
        height: 22px;
        border: 1px solid;
        border-radius: 22px;
    }

        .icon::before {
            display: block;
            position: absolute;
            content: '';
            top: 9px;
            left: 5px;
            width: 10px;
            height: 2px;
            background: currentColor;
        }

        .icon::after {
            display: block;
            position: absolute;
            content: '';
            top: 5px;
            left: 9px;
            width: 2px;
            height: 10px;
            background: currentColor;
        }

    button[aria-expanded='true'] {
        color: var(--clr-primary);
    }

        button[aria-expanded='true'] .icon::after {
            width: 0;
        }

        button[aria-expanded='true'] + .accordion-content {
            opacity: 1;
            max-height: 330px;
            transition: all 200ms linear;
            will-change: opacity, max-height;
        }

    .accordion-content {
        opacity: 0;
        max-height: 0;
        color: var(--clr-black-90);
        overflow: hidden;
        transition: opacity 200ms linear, max-height 200ms linear;
        will-change: opacity, max-height;
    }

        .accordion-content p {
            font-size: 1rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
        }

        .accordion-content h3 {
            color: var(--clr-primary);
            margin-top: 1rem;
        }

    h2 {
        text-align: center;
        font-size: 20px;
        line-height: 1.1;
        
    }

    .accordion-content a {
        color: var(--clr-primary);
    }

    .icon-1 {
        height: 45px;
        width: 45px;
        margin-inline: auto;
    }
</style>
