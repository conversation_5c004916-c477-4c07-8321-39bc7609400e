import axios from 'axios';
import TenantService from "./tenant-service";

let baseUrl: string;

switch (true) {
  case window.location.hostname.indexOf("localhost") >= 0:
    baseUrl = "https://localhost:7150/api";
    break;
  case window.location.hostname.indexOf("test") >= 0 || window.location.hostname.indexOf("testing") >= 0:
    baseUrl = "http://neu-r4g-app-rewards4platform-api-testing.azurewebsites.net/api";
    break;
  case window.location.hostname.indexOf("staging") >= 0:
    baseUrl = "https://neu-r4g-app-rewards4platform-api-staging.azurewebsites.net/api";
    break;
  default:
    baseUrl = "https://neu-r4g-app-rewards4platform-api/api";
}

const api = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
});

// Request interceptor to add tenant header
api.interceptors.request.use(
  (config: any) => {    
    const tenantName = TenantService.getTenantName();
    if (tenantName) {
      config.headers = config.headers || {};
      config.headers['X-Tenant-Name'] = tenantName;      
    } else {
      console.warn('No tenant name available for request');
    }

    return config;
  },
  (error: any) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response: any) => response,
  (error: any) => {
    if (error.response?.status === 401) {
      window.location.href = '/signin';
    } else if (error.response?.status === 400 && error.response?.data?.error === 'Tenant Required') {
      console.error('[Axios] Tenant required error:', error.response.data);
      alert('Tenant configuration error. Please contact support.');
    }
    return Promise.reject(error);
  }
);

export default api;