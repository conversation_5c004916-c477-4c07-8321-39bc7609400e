import api from "./axios";
import IndexPageDataResponse from "../responses/IndexPageDataResponse";

export class PageService {
  async getIndexPageContent() {  
    try {      
      const response = await api.get("/page/index");
      return response.data as IndexPageDataResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error getting page content: " + error.message);
      }
    }
  }
}

export default new PageService();