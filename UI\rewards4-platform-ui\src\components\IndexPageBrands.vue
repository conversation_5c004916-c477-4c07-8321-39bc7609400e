<template>
  <section>
    <div class="container" v-if="retailerBrandImages">
      <div class="images">
        <img :src= retailerBrandImages.firstRetailerImageUrl />
        <img :src= retailerBrandImages.secondRetailerImageUrl />
        <img :src= retailerBrandImages.thirdRetailerImageUrl />
        <img :src= retailerBrandImages.fourthRetailerImageUrl />
        <img :src= retailerBrandImages.fifthRetailerImageUrl />
        <img :src= retailerBrandImages.sixthRetailerImageUrl />
        <!-- repeat as needed -->
      </div>
      <div class="text">
        <h3>Collect points with thousands of top brands</h3>
        <p>
          Thousands of top brands pay us when you shop and we share that with
          you in the form of points. Simply click through to the brand you wish
          to shop with from your Rewards4Imps account and we'll do the rest
        </p>
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { defineComponent } from "vue";
export default {
  props: {
    retailerBrandImages: Object,
  },
  setup(props) {
    return {
      retailerBrandImages: props.retailerBrandImages,
    };
  }
};


</script>

<style scoped>
section {
  background-color: #f3f3f3;
  background: radial-gradient(#fff 50%, #ecf0f3);
}

.container {
  max-width: min(100% - 30px, 1000px);
  display: grid;
  grid-template-columns: 440px 1.5fr;
  margin-inline: auto;
  padding-block: 3rem;
}

.images {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  justify-content: center;
  gap: 0.5rem;
  margin-block: 1rem;
  padding-right: 3rem;
}

.images img {
  border-radius: 10px;
  box-shadow: 0 7px 15px -7px #333333ab;
  border: solid 2px #ecf0f3;
  width: 100%;
  max-width: 174px;
}

.image-wrapper p {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  z-index: 11;
  font-size: 30px;
  text-align: center;
  line-height: 1;
  text-decoration: underline;
}

.text {
  align-content: center;
  display: grid;
  gap: 0.3rem;
}

.text h3 {
  color: black;
  font-size: 45px;
  font-weight: 600;
  line-height: 1;
}

@media (max-width: 800px) {
  .container {
    grid-template-columns: 1fr;
    padding-block: 2rem;
  }

  .text h3 {
    font-size: 35px;
  }

  .text {
    text-align: center;
    margin-inline: auto;
    order: -1;
  }

  .images img {
    max-width: 90px;
  }

  .images {
    padding-right: 0rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }
}
</style>
