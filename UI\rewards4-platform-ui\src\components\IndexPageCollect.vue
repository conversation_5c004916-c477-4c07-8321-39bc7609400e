<template>


    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Caveat+Brush&display=swap" rel="stylesheet">

    <div class="container">
        <div class="text">
            <h3>Collect points with Lincoln City</h3>
            <p>We've made it super simple for you to collect points with the club you love! Collect points when buying your Lincoln City Home Match Tickets, Memberships or Hospitality. The more points you collect, the bigger the saving you will make.</p>
        </div>
        <div class="images">
            <div class="image-wrapper">
                <img src="https://images.unsplash.com/photo-1544366981-2150548c9c1c?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NTQwNTczNzd8&ixlib=rb-4.1.0&q=80&w=400" alt="">
                <div class="overlay-text">
                    <span>Home</span>
                    <p>Match Tickets</p>
                </div>
       
            </div>
            <div class="image-wrapper">
                <img src="https://images.unsplash.com/photo-1501386761578-eac5c94b800a?..." alt="">
                <div class="overlay-text">
                    <span>Gold</span>
                    <p>Memberships</p>
                </div>
            </div>
            <div class="image-wrapper">
                <img src="https://images.unsplash.com/photo-1547478267-54ce493dd7d6?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NTQwNTc0Mjl8&ixlib=rb-4.1.0&q=80&w=400" alt="">
                <div class="overlay-text">
                    <span>Great</span>
                    <p>Hospitality</p>
                </div>
            </div>
            <div class="image-wrapper">
                <img src="https://images.unsplash.com/photo-1617144519956-bba853571334?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3wzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE3NTQwNTc0OTl8&ixlib=rb-4.1.0&q=80&w=400" alt="">
                <div class="overlay-text">
                    <span>Club</span>
                    <p>Merchandise</p>
                </div>
            </div>
                <!-- repeat as needed -->
            </div>
    </div>
</template>

<style scoped>

    .container {
        max-width: min(100% - 30px, 1000px);
        display: grid;
        grid-template-columns: 1.5fr 440px;
        margin-inline: auto;
        padding-block: 3rem;
    }

    .images {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap:0.5rem;
        margin-block:1rem;
    }
        .images img {
            filter: brightness(0.7) contrast(1.1);
            border-radius: 10px;         
        }


    .image-wrapper {
        position: relative;
        border-radius: 10px;
        display: inline-block;
        border: solid 3px white;
        box-shadow: 0 7px 15px -7px #333333ab;
        text-wrap: nowrap;
    }

        .image-wrapper::after {
            content: "";
            position: absolute;
            top: 0;
            border-radius: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(187deg, rgba(252, 176, 69, 0) 0%, #0d0d0d 69%);
            opacity: 0.7; /* optional: make it slightly transparent */
            pointer-events: none; /* ensures the image underneath is still clickable */
        }


    .overlay-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 11;
    }

    .image-wrapper span {
        color: white;
        font-size: 18px;
        text-align: center;
        display: block;
        line-height: 1;
    
    }

    .image-wrapper p {     
        color: white;   
        font-size: 25px;
        text-align: center;
        line-height: 1;
     
    }

    .text {
        align-content: center;
        padding-right: 1rem;
        display: grid;
        gap: 0.3rem;
        max-width: 580px;
    }

        .text h3 {
            color: var(--clr-primary);
            font-size: 45px;
            font-weight: 600;
            line-height: 1;
        }

    @media (max-width: 800px) {
        .container {
            grid-template-columns: 1fr;
            padding-top: 2rem;
            padding-bottom: 1rem;
        }
        .text h3 {
            font-size: 35px;
        }


        .image-wrapper {
            margin-block:1rem;
        }

        .text {
            text-align: center;
            padding-right: 0rem;
            margin-inline: auto;
        }


        .images {
            display: grid;
            grid-template-columns: 200px 200px 200px 200px;
            grid-template-rows: 1fr;
            gap: .7rem;
            overflow: hidden;
            flex-wrap: nowrap;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
            
        }
    }

</style>