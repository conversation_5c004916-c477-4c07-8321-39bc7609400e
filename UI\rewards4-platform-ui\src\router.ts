import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON>, RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
        name: "root",
        component: () => import("./views/IndexPageView.vue"),
  },
  {
    path: "/signin",
    name: "signin",
    component: () => import("./components/SigninView.vue"),
  },

  {
    path: "/home",
      name: "home",
      component: () => import("./views/HomePageView.vue"),
  },
  {
    path: "/collect",
    name: "collect",
    component: () => import("./views/CollectRetailerView.vue"),
  },
  {
    path: "/JoinSign",
    name: "Join",
    component: () => import("./views/JoinSignUpView.vue"),
  },
  {
    path: "/retailer-redirect",
    name: "retailer-redirect",
    component: () => import("./views/RetailerRedirect.vue"),
  },
  {
    path: "/my-account",
    name: "my-account",
    component: () => import("./views/MyAccountView.vue"),
  },
  {
    path: "/forgot-password",
    name: "forgot-password",
    component: () => import("./views/ForgotPasswordView.vue"),
  },
  {
    path: "/change-password",
    name: "change-password",
    component: () => import("./views/ChangePasswordView.vue"),
  },
  {
    path: "/spend",
    name: "sprend",
    component: () => import("./views/SpendView.vue"),
  },
  {
    path: "/spend/my-goal",
    name: "my-goal",
    component: () => import("./views/MyGoalView.vue"),
  },
  {
    path: "/help-centre",
    name: "help-centre",
    component: () => import("./views/HelpCentreView.vue"),
  },
  {
    path: "/home-collect",
    name: "home-collect",
    component: () => import("./views/CollectHomeView.vue"),
  },
  {
    path: "/weekly-trivia",
    name: "weekly-trivia",
    component: () => import("./views/WeeklyTriviaView.vue"),
  },

  {
    path: "/spend/save/spend-on",
    name: "spend-on",
    component: () => import("./views/SpendOnView.vue"),
  },
  {
    path: "/howtoshoponline",
    name: "howtoshoponline",
    component: () => import("./views/HowItWorksView.vue"),
  },

  {
    path: "/terms/:termsSection?",
    name: "terms",
    component: () => import("./views/MemberAgreementView.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
});

export default router;
