<template>
    <section>
        <div class="bg-img">
            <div class="loginsignup-containter">
                <input type="radio" :checked="isLogin" name="login-signup" id="login" />
                <label for="login" class="login">LOG IN</label>

                <input type="radio" :checked="!isLogin" name="login-signup" id="signup" />
                <label for="signup" class="signup">CREATE ACCOUNT</label>

                <div class="content login-content">
                    <Login></Login>
                </div>

                <div class="content signup-content">
                    <signup></signup>
                </div>
            </div>
        </div>
    </section>
</template>

<script lang="ts">
    import { defineComponent, ref } from "vue";

    import Signup from "../components/Signup.vue";
    import Login from "../components/Login.vue";



export default defineComponent({
  components: {
    Signup,
    Login,
    },
      setup(){

          const isLogin = ref(true);

     

     
    return {
        isLogin,
        
    };

  }
});
</script>

<style scoped>

    section {
        background-image: url('https://images.unsplash.com/photo-*************-7ed62a19d5f0?crop=entropy&cs=srgb&fm=jpg&ixid=****************************************************&ixlib=rb-4.1.0&q=85');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .bg-img {
        backdrop-filter: brightness(0.6) contrast(1.1) saturate(1.5);
        padding-block: 2rem;
    }

    .loginsignup-containter {
        max-width: min(100% - 15px, 500px);
        margin-inline: auto;
        display: grid;
        grid-template-columns: 1fr 1fr;
        background-color: #cbc9c9a8;
        border-radius: 10px;
        box-shadow: 0.063em 0.75em 1.563em rgba(0, 0, 0, 0.78);
    }

        .loginsignup-containter label {
            padding-block: 1.5rem;
            display: flex;
            justify-content: center;
            cursor: pointer;
            width: 100%;
            font-size: 18px;
            color: var(--clr-black-90);
            border-radius: 10px 10px 0 0;
        }

    @media (max-width: 350px) {
        .loginsignup-containter label {
            font-size: 14px;
        }

    
    }

        .loginsignup-containter input {
            display: none;
        }

        .loginsignup-containter .content {
            display: none;
            opacity: 1;
        }

    /* Display content when the input is checked */
    #login:checked ~ .login-content,
    #signup:checked ~ .signup-content {
        display: grid;
        grid-column: span 2;
        opacity: 1;
    }

    input:checked + label {
        background: #ffffffbf;
        outline: none !important;
        text-decoration: none;
        z-index: 1;
    }

    /* You can also prevent the labels from being focusable */
    label {
        cursor: pointer;
        user-select: none; /* Prevent text selection */
    }

        label:hover {
            text-decoration: underline;
        }


    input:-webkit-autofill {
        color: #000 !important;
        border: 1px solid #ccc;
        box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.1);
    }
</style>
