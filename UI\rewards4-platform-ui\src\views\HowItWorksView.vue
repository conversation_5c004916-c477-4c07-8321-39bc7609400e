<template>
    <div class="how-section">
        <Video></Video>
        <div class="how-container">

            <Steps></Steps>
            <!--<retailerCards></retailerCards>-->
            <TopBrands></TopBrands>
          
            <Faqs></Faqs>
        </div>

    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import Video from '../components/Video.vue';
    import Steps from '../components/123Steps.vue';
    import retailerCards from '../components/TopBrandsCards.vue';
    import Faqs from '../components/Faqs.vue';
    import TopBrands from '../components/TopBrands.vue';

    export default defineComponent({
        name: 'HowItWorks',
        components: {
            Video,
            Steps,
            retailerCards,
            Faqs,
            TopBrands
        },
    });
</script>

<style scoped>
    .how-section {
        background-color: #ecf0f3;
        background: radial-gradient(#fff 50%, #ecf0f3);
    }


    .how-container {
        max-width: min(100%, 1000px);
        display: grid;
        margin-inline: auto;
        overflow: hidden;
        background: white;
        margin-top: -37px;
        box-shadow: 0px -10px 14px 0px #33333324;
        display: grid;
        gap: 2rem;
        position: relative;
        z-index: 2;
        border-radius: 20px 20px 0 0;
        z-index:1;
    
    
    }
</style>
