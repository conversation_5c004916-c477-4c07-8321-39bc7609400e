<template>
    <div class="main-title">Email Preferences for: <EMAIL></div>

    <div class="unsub-type" v-for="(item, index) in items" :key="index">
        <div>
            <div class="title">{{ item.title }}</div>
            <p class="description">{{ item.description }}</p>
        </div>
        <div class="btn btn-pill toggle">
            <input type="checkbox" class="checkbox" :id="'toggle-' + index" />
            <label class="knob" :for="'toggle-' + index"></label>
            <div class="btn-bg"></div>
        </div>
    </div>

    <button class="unsubscribe-btn">Unsubscribe from all</button>

    <small>Unsubscribing from all Rewards4Imps emails will mean that we won't send you any more marketing emails but you will still receive service emails relating to your Rewards4Imps account (including, but not limited to, information about points you have collected or spent with partners, as well as any updates to our services).</small>
    <small>To stop receiving service emails, you will need to close your account by contacting our Member Services team, via our a <a href="#"> Help Centre</a>.</small>
</template>

<script>
export default {
  data() {
    return {
      items: [
        {
          title: "Lincoln City",
          description:
            "Stay close to the best ways to collect points through your interactions with Rewards4Imps.",
        },
        {
            title: "Betting",
          description:
            "The latest news and offers from our betting partners.",
        },
        {
            title: "Shopping",
          description:
            "Keep up to date with the latest offers available when shopping online.",
            },
            {
                title: "Competitions & Exclusive Offers",
                description:
                    "Exclusive offers to grow your points total and money-can't buy prizes.",
            },
      ],
    };
  },
};
</script>

<style scoped>
    .unsub-type {
        display: grid;
        justify-content: space-between;
        border-bottom: 1px solid #7b786538;
        grid-template-columns: 1fr auto;
        padding: 1rem;
        align-items: baseline;
    }

    .main-title {
        font-size: 18px;
        font-weight: 600;
        color: #3f3f3f;
        padding: 0rem 0.5rem 0.5rem 0.5rem;
        border-bottom: 1px solid #7b786538;
    }
    .title {
        font-size: 18px;
        font-weight: 600;
        color: #3f3f3f;
    }
    .description {
        font-size: 14px;
        color: #3f3f3f;
    }

    .knob,
    .btn-bg {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }

    .btn {
        position: relative;
        top: 50%;
        width: 74px;
        height: 36px;
        overflow: hidden;
    }

        .btn.btn-pill,
        .btn.btn-pill > .btn-bg {
            border-radius: 100px;
        }

    .btn-pill .checkbox {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 0;
        margin: 0;
        opacity: 0;
        cursor: pointer;
        z-index: 3;
    }

    .knob {
        z-index: 2;
    }

    .btn-bg {
        width: 100%;
        background-color: #00800047;
        transition: 0.3s ease all;
        z-index: 1;
    }

    /* Toggle styles */
    .toggle .knob:before {
        content: "YES";
        position: absolute;
        top: 4px;
        left: 4px;
        width: 28px;
        height: 28px;
        color: #fff;
        font-size: 10px;
        font-weight: bold;
        text-align: center;
        line-height: 1;
        padding: 9px 4px;
        background-color: green;
        border-radius: 50%;
        transition: 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15) all;
    }

    .toggle .checkbox:checked + .knob:before {
        content: "NO";
        left: 42px;
        background-color: #f44336;
    }

    .toggle .checkbox:checked ~ .btn-bg {
        background-color: #fcebeb;
    }

    .toggle .knob,
    .toggle .knob:before,
    .toggle .btn-bg {
        transition: 0.3s ease all;
    }

    .unsubscribe-btn {
        border:none;
        outline:none;
        background-color: transparent;
        text-decoration: underline;
        padding:0.5rem;
        display:block;
    }

    small {
        padding:0.3rem;
        display: block;
    }


    small a {
        color: black;
        font-weight:600;
    }
</style>
