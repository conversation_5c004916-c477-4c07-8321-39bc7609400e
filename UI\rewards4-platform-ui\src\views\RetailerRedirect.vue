<template>
    <section>
        <div class="retailer-redirect">
            <div class="container">
                <h1>ONE MOMENT PLEASE...</h1>
                <div class="grid">
                    <div class="image-wrapper">
                        <img class="logo float-up-3d" src="../assets/images/logos/LincolnCityLogo.svg" />
                    </div>
                    <div class="image-wrapper">
                        <img class="float-down-3d" src="../assets/images/TEMPDEV/justeatsquare2.png" />
                    </div>
                </div>
                <p>
                    If you are not redirected after 3 seconds
                    <a href="#">Click Here</a> to continue to Just Eat.
                </p>
            </div>
        </div>
    </section>
</template>

<style scoped>
    section {
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .container {
        max-width: min(100% - 30px, 500px);
        display: grid;
        margin-inline: auto;
        padding-block: 3rem;
        text-align: center;
    }

    .grid {
        display: flex;
        justify-content: center;
        padding-block: 2rem;
        gap: 2rem;
    }

    .image-wrapper {
        display: inline-block;
        position: relative;
        transform-style: preserve-3d;
    }

        .image-wrapper img {
            border-radius: 10px;
            max-width: 100%;
            height: 100%;
            display: block;
            outline: 3px solid #e0e0e0;
            border: 3px solid #ffffff;
        }

        .image-wrapper::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 0;
            width: 100%;
            height: 20px;
            border-radius: 30%;
            background: #000000;
            background: radial-gradient(circle,rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0.99) 55%, rgba(135, 135, 135, 0) 100%);
            perspective: 100px;
            
            z-index: -1;
        }

    /* Logo styling */
    .logo {
        background-color: red;
        padding: 1rem;
        width: 136px;
        
    }
      

        a {
            color: black;
            font-weight: 600;
        }

    /* Floating animations (image only) */
    .float-up-3d {
        animation: floatUp3D 1.5s ease-in-out infinite alternate;
    }

    .float-down-3d {
        animation: floatDown3D 1.5s ease-in-out infinite alternate;
    }

    @keyframes floatUp3D {
        0% {
            transform: translateY(0) scale(1) rotateX(0deg);
        }

        100% {
            transform: translateY(-15px) scale(1.05) rotateX(20deg);
        }
    }

    @keyframes floatDown3D {
        0% {
            transform: translateY(-15px) scale(1.05) rotateX(20deg);
        }

        100% {
            transform: translateY(0) scale(1) rotateX(0deg);
        }
    }

    /* Shadow animations (shadow stays still, changes blur/size/opacity) */
    .image-wrapper:first-child::after {
        animation: shadowUp 1.5s ease-in-out infinite alternate;
    }

    .image-wrapper:last-child::after {
        animation: shadowDown 1.5s ease-in-out infinite alternate;
    }

    @keyframes shadowUp {
        0% {
            transform: scale(1.1);
            opacity: 0.4;
            filter: blur(4px);
        }

        100% {
            transform: scale(1.2);
            opacity: 0.3;
            filter: blur(5px);
        }
    }

    @keyframes shadowDown {
        0% {
            transform: scale(1.1);
            opacity: 0.3;
            filter: blur(5px);
        }

        100% {
            transform: scale(1.1);
            opacity: 0.4;
            filter: blur(4px);
        }
    }
</style>
