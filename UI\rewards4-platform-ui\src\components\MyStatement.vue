<template>
    <div class="statement">
        <p class="header-text">View all your transactions and activity in your statement below. Are you missing any points? <a href="#">Click Here</a>  to start a missing points claim, or to read our   <a href="#">FAQs</a>.</p>
        <div class="transaction">
            <div class="statement-grid">

                <div class="transaction-details">
                    <div class="description">
                        Currys
                    </div>
                    <div>
                        <div class="transaction-date"><strong>Transaction date: </strong> 30/04/2025</div>
                        <div class="active-date"><strong>Active date: </strong>30/04/2025</div>
                    </div>
                </div>
                <div class="flex">
                    <div class="points">-10pts</div>
                    <div class="status">Paid</div>
                </div>
            </div>
        </div>



        <div class="transaction">
            <div class="statement-grid">

                <div class="transaction-details">
                    <div class="description">
                      HP
                    </div>
                    <div>
                        <div class="transaction-date"><strong>Transaction date: </strong> 30/04/2025</div>
                        <div class="active-date"><strong>Active date: </strong>30/04/2025</div>
                    </div>
                </div>
                <div class="flex">
                    <div class="points">1pts</div>
                    <div class="status">
                        declined
                    </div>
                </div>
            </div>
        </div>


        <div class="transaction">
            <div class="statement-grid">

                <div class="transaction-details">
                    <div class="description">
                        End of Season Points Expiry
                    </div>
                    <div>
                        <div class="transaction-date"><strong>Transaction date: </strong> 30/04/2025</div>
                        <div class="active-date"><strong>Active date: </strong>30/04/2025</div>
                    </div>
                </div>
                <div class="flex">
                    <div class="points">-100000pts</div>
                    <div class="status">Available to use</div>
                </div>
            </div>
        </div>
    </div>
</template>



<style scoped >

    .statement {
        gap: 1rem;
        display: grid;
    }

    .header-text {
        text-align: center;
        color: #3f3f3f;
        border-bottom: 1px solid #7b786538;
        padding: 1.5rem 1rem 1rem 1rem;
        margin-inline: auto;
    }
    .header-text a {
        font-weight:600; 
        color: black;
    }

    .statement-grid {
        display: grid;
        grid-template-columns: 1fr 103px;
    }

    .transaction {
        border-bottom: 1px solid #7b786538;
        padding: 0.3rem 0.5rem;
    }

    .transaction-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
      
    }

    .description {
        font-size: 18px;
        font-weight: 600;
        color: #3f3f3f;
    }

    .points {
        background-color: green;
        padding: 0.5rem;
        color: white;
        border-radius: 12rem;
        text-align: center;
      margin-block:0.5rem;
    
    }

  

    .status {
        color: green;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        line-height:1;
      text-transform: uppercase;
    }

    @media (max-width: 700px){
        .description {
  
           
        }

        .transaction-details {
            grid-template-columns: 1fr;
        }

    }

    @media (max-width: 370px) {
        .statement-grid {
         
            grid-template-columns: 1fr;
            gap:1rem;
        }
    }

</style>