v<template>

    <div class="footer-accordion">
        <div class="footer-accordion-item">
            <button id="footer-accordion-button-1" aria-expanded="false"><span class="footer-accordion-title">Here To Help</span><span class="icon" aria-hidden="true"></span></button>
            <div class="footer-accordion-content">
                <a href="/help-centre">
                    Help Centre
                </a>
                <a href="#">
                    FAQs
                </a>
                <a href="#">
                    Safer Gambling
                </a>
             

            </div>
        </div>


        <div class="footer-accordion-item">
            <button id="footer-accordion-button-2" aria-expanded="false"><span class="footer-accordion-title"> How it Works</span><span class="icon" aria-hidden="true"></span></button>
            <div class="footer-accordion-content">

                <a href="/howtoshoponline">
                    How To Shop Online
                </a>
         



            </div>
        </div>


        <div class="footer-accordion-item">
            <button id="footer-accordion-button-2" aria-expanded="false"><span class="footer-accordion-title"> Get Involved</span><span class="icon" aria-hidden="true"></span></button>
            <div class="footer-accordion-content">

                <a href="#">
                    Careers
                </a>

          



            </div>
        </div>


        <div class="footer-accordion-item">
            <button id="footer-accordion-button-2" aria-expanded="false"><span class="footer-accordion-title">Legal Stuff</span><span class="icon" aria-hidden="true"></span></button>
            <div class="footer-accordion-content">
                <a href="/terms">
                    Member Agreement
                </a>
                <a href="#">
                    Privacy Policy
                </a>
                <a href="#">
                    Cookie Policy
                </a>

            </div>
        </div>

       
    </div>

</template>

<script lang="ts">
    import { defineComponent, onMounted } from "vue";
    import { useRouter } from "vue-router";

    export default defineComponent({
        name: "Faqs",
        setup() {
            const router = useRouter();

            onMounted(() => {
                // footer-accordion functionality
                const items = document.querySelectorAll(".footer-accordion button");

                function togglefooteraccordion(this: HTMLButtonElement) {
                    const isExpanded = this.getAttribute("aria-expanded") === "true";
                    this.setAttribute("aria-expanded", String(!isExpanded));
                }

                items.forEach(item =>
                    item.addEventListener("click", togglefooteraccordion)
                );
            });
        }
    });
</script>


<style scoped>
    .footer-accordion {
      
    }

        .footer-accordion .footer-accordion-item {
            border-bottom: 1px solid white;
      
        }

         /*   .footer-accordion .footer-accordion-item button[aria-expanded='true'] {
                border-bottom: 1px solid white;
            }*/

    button {
        position: relative;
        display: block;
        text-align: left;
        width: 100%;
        padding:1rem 0 1rem 0;
        color: white;
        font-size: 18px;
        font-weight:600;
        border: none;
        background: none;
        outline: none;
    }

        button:hover, button:focus {
            cursor: pointer;
            color: white;
        }

            button:hover::after, button:focus::after {
                cursor: pointer;
                color: black;
                border: 1px solid white;
            }

    .footer-accordion-title {

        
    }

    .icon {
        display: inline-block;
        position: absolute;
        top: 18px;
        right: 0;
        width: 22px;
        height: 22px;
        border: 1px solid;
        border-radius: 22px;
    }

        .icon::before {
            display: block;
            position: absolute;
            content: '';
            top: 9px;
            left: 5px;
            width: 10px;
            height: 2px;
            background: currentColor;
        }

        .icon::after {
            display: block;
            position: absolute;
            content: '';
            top: 5px;
            left: 9px;
            width: 2px;
            height: 10px;
            background: currentColor;
        }

    button[aria-expanded='true'] {
        text-decoration: underline;
        text-decoration-thickness: 1px;
        text-underline-offset: 5px; /* correct property */
 
    }

        button[aria-expanded='true'] .icon::after {
            width: 0;
        }

        button[aria-expanded='true'] + .footer-accordion-content {
            opacity: 1;
            max-height: 100px;
            transition: all 200ms linear;
            will-change: opacity, max-height;
            margin-top: -10px;
        }

    .footer-accordion-content {
        display: grid;
        opacity: 0;
        max-height: 0;
        color: white;
        overflow: hidden;
        transition: opacity 200ms linear, max-height 200ms linear;
        will-change: opacity, max-height;
        
    }

        .footer-accordion-content p {
            font-size: 16px;
          
            margin-bottom: 0.5rem;
            
        }


        .footer-accordion-content a {
            color: white;
            font-size: 16px;
            margin-bottom: 0.5rem;
            text-decoration: none;
        }


</style>
