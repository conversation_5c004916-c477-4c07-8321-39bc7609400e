<template>
    <section class="cta-section">
        <div class="l-flex-center">
            <a href="/join" class="btn-link-chunky btn-theme-1">
                Join for FREE
                <span class="arrow1">></span><span class="arrow2">></span>
            </a>
        </div>
    </section>
</template>

<style scoped>


    .cta-section {
        background-color: #f3f3f3;
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .btn-link-chunky {
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border-radius: 10px;
        padding-block: 1.2rem;
        min-width: 300px;
        font-size: 1rem;
        font-weight: 900;
        color: #fff !important;
        background-color: #e4002b !important;
        text-decoration: none;
        box-shadow: inset 7px 10px 15px -7px #ffffff9e, 0 7px 15px -7px #333333;
        transition: letter-spacing 0.5s ease-in-out; /* Smooth animation */
    }

    .l-flex-center {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-block: 2rem;
    
        letter-spacing: 0px;
    }

    span {
        font-size: inherit;
        font-weight: 900;
     
    }

    /* Target span when parent <a> is hovered */
    a:hover {
        letter-spacing: 1.5px;
    }

    .arrow {
        font-size: 20px;
        font-weight: 900;
        opacity: 1;
    }

    /* DESKTOP: Animate only on hover */
    a:hover .arrow1,
    a:hover .arrow2 {
        animation: fadeLoop 1s infinite;
    }

    a:hover .arrow2 {
        animation-delay: 0.3s;
    }

    /* MOBILE: Always animate */
    @media (max-width: 768px) {
        .arrow1,
        .arrow2 {
            animation: fadeLoop 1.5s infinite;
        }

        .arrow2 {
            animation-delay: 0.3s;
        }
    }

    /* Keyframes for fade effect */
    @keyframes fadeLoop {
        0%, 100% {
            opacity: 1;
        }

        50% {
            opacity: 0.3;
        }
    }

</style>