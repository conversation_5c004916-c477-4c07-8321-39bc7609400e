<template>
    <div class="hero-banner">
        <div class="container">
            <div class="my-goal">
                <div class="my-goal-header">
                    Members who set their goal <span>Save 6X More</span>
                </div>
                <div class="my-goal-text">
                    <p>Tell us what you want to save towards and we will help you get there.</p>
                </div>

                <!-- Dropdown + Button -->
                <div class="my-goal-actions">
                    <select class="goal-select">
                        <option disabled selected>please select</option>
                        <option>Home Tickets</option>
                        <option>Season Ticket</option>
                        <option>Away Shirt</option>
                        <option>Home Shirt</option>

                    </select>
                    <button class="goal-button">Set My Goal >></button>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
    .hero-banner {
        position: relative;
        width: 100vw;
        height: 550px;
        ; /* adjust height as needed */
        background-image: url("../assets/images/TEMPDEV/Lincoln_HomepageLoggedOut_SignIn_HeroBanner_Desktop.png");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }

    .container {
        position: relative;
        margin-inline: auto;
        height: 100%;
        backdrop-filter: brightness(0.5);
    }

    .my-goal {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%); /* ? centers perfectly */
        color: white;
        max-width: 350px;
        background: rgba(228, 0, 43, 0.85);
        border: solid 2px rgb(255 255 255 / 43%);
        border-radius: 10px;
        padding: 2rem;
        box-shadow: 0.063em 0.75em 1.563em rgba(0, 0, 0, 0.78);
        max-width: 435px;
        width: calc(100% - 20px);
    }

    p {
        text-align: center;
        line-height: 1;
        padding-top: 1rem;
    }

    .my-goal-header {
        font-size: 60px;
        font-weight: bold;
        display: grid;
        line-height: 1;
        text-align: center;
        font-family: "Bebas Neue", impact;
    }

        .my-goal-header span {
            color: black; /* highlight */
            font-weight: bold;
            font-family: "Caveat Brush";
            font-size: 55px;
            text-transform: uppercase;
        }

    .goal-select {
        padding: 1rem;
        margin-top: 1.5rem;
        border-radius: 2px;
        border: none;
        font-size: 1rem;
    }



    .my-goal-actions {
        display: grid;
        gap: 0.7rem;
    }

        .my-goal-actions button {
            width: 100%;
            padding: 1.3rem;
            background-color: black;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.1rem;
            cursor: pointer;
        }


    @media (max-width: 360px) {
        .my-goal-header {
            font-size: 45px;
        }

            .my-goal-header span {
                font-size: 50px;
                line-height:1;
            }
    }
</style>
