<template>
    <div class="bg">
        <div class="grid">
            <heroCarousel></heroCarousel>
            <retailerCards></retailerCards>
            <stripBanner></stripBanner>
            <masonryCards></masonryCards>
        </div>
    </div>
</template>


<script lang="ts">
    import { defineComponent } from 'vue';

    import heroCarousel from '../components/HeroCarousel.vue';
    import retailerCards from '../components/RetailerCards.vue';
    import masonryCards from '../components/MasonryCards.vue';
    import stripBanner from '../components/StripBanner.vue';


    export default defineComponent({
        name: 'home',
        components: {
            heroCarousel,
            retailerCards,
            stripBanner,
            masonryCards
        },
    });
</script>

<style scoped>
    .bg {
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .grid {
        display: grid;
        gap: 1rem;
        padding-bottom: 1rem;
    }
</style>