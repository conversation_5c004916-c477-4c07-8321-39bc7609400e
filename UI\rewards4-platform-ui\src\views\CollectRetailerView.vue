<template>
  <section>
      <div class="container">
          <div class="retailer-page">
              <div class="retailer-page-info">
                  <div class="retailer-images">
                      <img class="retailer-img" :src="img" />
                      <img class="retailer-logo" :src="logo" />
                  </div>
                  <div class="retailer-content">
                      <div class="retailer-content-grid">

                          <div class="retailer-link">
                              <a href="/retailer-redirect" class="btn-link-chunky btn-theme-1">
                                  Shop to Collect Points
                                  <span class="arrow1">></span><span class="arrow2">></span>
                              </a>
                          </div>

                          <div class="retailer-text">
                              <p>Enjoy collecting points when you shop online with Just Eat. Just remember to click through us here first so we can track your purchase and reward you the right amount of points.</p>
                          </div>

                          <div class="retailer-rates">
                              <div class="retailer-rate">
                                  <div>New Customer</div>
                                  <div class="retailer-rate-flex">
                                      <div>29 points per &pound;1</div>
                                      <img class="icon" :src="linkIcon" />
                                  </div>
                              </div>
                              <div class="retailer-rate">
                                  <div>New Customer</div>
                                  <div class="retailer-rate-flex">
                                      <div>29 points per &pound;1</div>
                                      <img class="icon" :src="linkIcon" />
                                  </div>
                              </div>
                              <div class="retailer-rate">
                                  <div>New Customer</div>
                                  <div class="retailer-rate-flex">
                                      <div>29 points per &pound;1</div>
                                      <img class="icon" :src="linkIcon" />
                                  </div>
                              </div>
                          </div>
                      </div>


                      <div class="retailer-page-terms">
                          <Terms></Terms>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import Terms from '../components/RetailerTerms.vue';

    export default defineComponent({
        name: 'Collect',
        components: {
            Terms,
        },
        setup() {
            const img: string = require('../assets/images/TEMPDEV/just-eat-new-header-nov24.jpg');
         /*   const img: string = require('../assets/images/TEMPDEV/debenhamsbanner110725.jpg');*/
            const logo: string = require('../assets/images/TEMPDEV/justeatsquare2.png');
            const linkIcon: string = require('../assets/images/TEMPDEV/arrow-up-right-from-square-solid-full.svg');

            return { img, logo,linkIcon };
        }
    });
</script>



<style scoped>
    section {
        background: radial-gradient(#fff 50%, #ecf0f3);
     
    }


    .container {
        max-width: min(100%, 1000px);
        display: grid;
        margin-inline: auto;
     


    }

    .retailer-text {
        text-align: center;
    }

    .retailer-page {
        border-radius: 10px;
        display: grid;
        gap: 2rem;
    }

    .retailer-images {
        position: relative;
        margin-bottom:-20px;
    }

        .retailer-images::after {
            content: "";
            position: absolute;
            top: 0;
            border-radius: 10px;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            background: linear-gradient(180deg, rgba(252, 176, 69, 0) 0%, #0d0d0d 69%);

            opacity: 0.7;
            pointer-events: none;
       
        }




    .retailer-img {
   
        filter: saturate(1.1) contrast(1.1) brightness(0.9);
        object-fit: cover;
        width: 100%;
        min-height: 145px;
        position: relative;
        z-index: 1;
    
    }


    .retailer-logo {
        border-radius: 10px;
        box-shadow: 0 7px 15px -7px #000000d1;
        border: solid 2px #ecf0f3;
        width: 100%;
        max-width: 130px;
    }

    .retailer-logo {
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3;
    }


    .retailer-content {
        background: radial-gradient(#fff 69%, #f4f4f4);
        box-shadow: 0px -10px 14px 0px #33333324;
        display: grid;
        gap: 3rem;
        position: relative;
        z-index: 2;
        border-radius: 20px 20px 0 0;
    }

        .retailer-content-grid {
        display: grid;
        gap: 1rem;
        max-width: min(100% - 30px, 600px);
        margin-inline: auto;

    }

    .retailer-link {
        padding-top: 4rem;
        display: flex;
        justify-content: center;
        
    }


    .retailer-rates {
        display: grid;
        gap:0.5rem;
    }



    .retailer-rate {
        display: flex;
        justify-content: space-between;
        background-color: #ecf0f3;
        background: radial-gradient(#fff 60%, #f8f8f8);
        border-radius: 10px;
        padding: 1.5rem 1rem;
        border: 2px solid #e7e7e794;
    }


        .retailer-rate .icon {
            width: 25px;
        }

    .retailer-rate-flex {
        display: flex;
        gap:1rem;
    }

    .btn-link-chunky {
        display: inline-block;
        cursor: pointer;
        text-align: center;
        border-radius: 10px;
        padding-block: 1.2rem;
        min-width: 230px;
        font-size: 1rem;
        font-weight: 900;
        color: #fff !important;
        background-color: #e4002b !important;
        text-decoration: none;
        box-shadow: inset 7px 10px 15px -7px #ffffff9e, 0 7px 15px -7px #333333;
        transition: letter-spacing 0.5s ease-in-out; /* Smooth animation */
    }

        span {
        font-size: inherit;
        font-weight: 900;
    }

    /* Target span when parent <a> is hovered */
    a:hover {
        letter-spacing: 1px;
    }

    a:active {
        transition: box-shadow 0.5s ease-in-out;
        box-shadow: none;
    }

    .arrow {
        font-size: inherit;
        font-weight: 900;
        opacity: 1;
    }

    /* DESKTOP: Animate only on hover */
    a:hover .arrow1,
    a:hover .arrow2 {
        animation: fadeLoop 1s infinite;
    }

    a:hover .arrow2 {
        animation-delay: 0.3s;
    }

    /* MOBILE: Always animate */
    @media (max-width: 768px) {
        .arrow1,
        .arrow2 {
            animation: fadeLoop 1.5s infinite;
        }

        .arrow2 {
            animation-delay: 0.3s;
        }

        .retailer-logo {
            max-width:100px;
        }
    }

    /* Keyframes for fade effect */
    @keyframes fadeLoop {
        0%, 100% {
            opacity: 1;
        }

        50% {
            opacity: 0.3;
        }
    }
</style>