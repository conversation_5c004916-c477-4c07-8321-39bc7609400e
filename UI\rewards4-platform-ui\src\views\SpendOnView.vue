<template>
    <section>
        <!--<img src="../assets/images/TEMPDEV/CustomCategory_Banner_Merch_1920x404px.png"
     alt="Lincoln City Merchandise Banner" />-->
        <picture>
            <!-- Mobile first -->
            <source srcset="../assets/images/TEMPDEV/CustomCategory_MobileBanner_Merch_820x370px.png"
                    media="(max-width: 768px)" />
            <!-- Desktop fallback -->
            <img src="../assets/images/TEMPDEV/CustomCategory_Banner_Merch_1920x404px.png"
                 alt="Lincoln City Merchandise Banner" />
        </picture>
        <div class="container">
            <div class="content">
                <h1>Spend your points on Rewards4Imps Merchandise</h1>
                <p>
                    Treat yourself to the latest Lincoln City Merchandise available in the Club Shop
                    by redeeming your Rewards4Imps points.
                </p>

                <!--<h2>Rewards4Imps Merchandise</h2>-->

                <div class="select-groups">
                    <div class="select-group">
                        <label for="goal-select-1" class="">Amount:</label>
                        <select id="goal-select-1" class="goal-select">
                            <option disabled selected>Please select</option>
                            <option>&pound;5</option>
                            <option>&pound;10</option>
                            <option>&pound;20</option>
                            <option>&pound;50</option>
                        </select>

                        <label for="goal-select-2" class="">Quantity:</label>
                        <select id="goal-select-2" class="goal-select">
                            <option disabled selected>Please select</option>
                            <option>1</option>
                            <option>2</option>
                            <option>3</option>
                            <option>4</option>
                            <option>5</option>
                            <option>6</option>
                            <option>7</option>
                        </select>
                    </div>
                    <button class="goal-button">Spend Points >></button>
                </div>

                <div class="instructions">
                    <h2>How do I spend my points?</h2>
                    <ul>
                        <li>
                            <strong>Please note:</strong> You must have activated your membership
                            and have a minimum of 1000 points (&pound;5.00) in your Rewards4Imps account
                            in order to redeem your points for a voucher.
                        </li>
                        <li>Simply select the points amount and quantity of vouchers you would like to redeem.</li>
                        <li>You will receive your unique voucher code via email from Rewards4Imps within 5 working days.</li>
                        <li>
                            Once you have received your voucher, you can then spend it by visiting the
                            <a href="https://www.eliteprosports.co.uk/lincoln-city/?gclid=CjwKCAjw2K6lBhBXEiwA5RjtCcEdhKjYtD9l6XnvzjV61Ebj8hz2_b_UzTNAbQaPY-_OZKxyr4NJTBoCysQQAvD_BwE"
                               target="_blank"
                               rel="noopener">
                                Lincoln City Club Shop
                            </a>.
                        </li>
                        <li>
                            From there, select your merchandise and add it to your basket.
                            At checkout, enter the voucher code to apply your discount.
                        </li>
                        <li>
                            <strong>Please note:</strong> Only one code can be used per transaction, and T&amp;Cs apply.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</template>

<style scoped>
    section {
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .container {
        max-width: min(100% - 0px, 600px);
        display: grid;
        margin-inline: auto;
        padding-bottom: 2rem;
    }

    h1 {
        
       text-align: center;
        font-size: 38px;
        color: #4c4c4c;
        font-weight: 600;
        line-height:1;
    }
    p {
        text-align: center;
        padding-block: 0.5rem;
    }


    h2 {
      
        font-size: 18px;
        color: #4c4c4c;
        font-weight: 600;
        padding-top:1rem;
    }
    .content {
        background-color: #fff;
        margin-top: -30px;
        border-radius: 20px 20px 0 0;
        box-shadow: 0 7px 15px -7px rgba(51, 51, 51, 0.67);
        border: 2px solid #fff;
        padding: 1rem 2rem;

    }

    img {
        width: 100%;
        object-fit: cover;
    }

    .select-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
       
    }
    label {
        padding-left: 10px;
    }

    .select-groups {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .goal-select {
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #ccc;
        font-size: 1rem;
    }



    .instructions ul {
        list-style-type: disc;
        padding-left: 1.2rem;
        padding-top:0.5rem;
        font-size:12px;
    }

    .instructions li {
      
        padding: 0.2rem;
    }

    button {
        width: 100%;
        padding: 1.3rem;
        background-color: black;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 1.1rem;
        cursor: pointer;
    }

 

    a {
        font-weight: 600;
        color: black;
    }

   @media(max-width: 500px) {
    h1 {
      
        font-size: 30px;
   
      
    }
   }


    @media(max-width: 400px) {
        h1 {
            font-size: 25px;
            text-align:center;
        }
        p {
            text-align: center;
            font-size: 13px;
        }

        .content {
            padding:1rem;
        }
    }
</style>
