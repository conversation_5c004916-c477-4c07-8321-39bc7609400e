<template>
    <myGoal></myGoal>
    <spendMyPoints></spendMyPoints>
</template>

<script lang="ts">
    import { defineComponent, ref } from "vue";

    import myGoal from "../components/MyGoal.vue";
    import spendMyPoints from "../components/SpendMyPoints.vue";





    export default defineComponent({
        components: {
            myGoal,
            spendMyPoints

        },
        setup() {



            return {

            };

        }
    });
</script>
