<template>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Caveat+Brush&family=Pacifico&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" />


    <div>
        <SiteNav />
        <Suspense>
            <template #default>
                <router-view />
            </template>
            <template #fallback>
                <div class="loading">Loading...</div>
            </template>
        </Suspense>
        <SiteFooter />
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import SiteNav from './components/SiteNav.vue';
    import SiteFooter from './views/SiteFooterView.vue';

    export default defineComponent({
        name: 'App',
        components: {
            SiteNav,
            SiteFooter,
        },
    });
</script>
