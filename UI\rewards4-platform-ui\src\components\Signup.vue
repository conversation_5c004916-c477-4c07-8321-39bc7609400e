
<template>
    <div class="form-container">
        <form @submit.prevent="submitForm" autocomplete="off" id="loginForm">
            <div class="wrapper">

                <div class="form-group">
                    <input class="email"
                           type="email"
                           id="emaillogin"
                           name="email"
                           placeholder="Name"
                           maxlength="320"
                           required />
                    <label for="emaillogin" class="placeholder-label">Name</label>
                </div>

                <div class="form-group">
                    <input class="email"
                           type="email"
                           id="emaillogin"
                           name="email"
                           placeholder="Email"
                           maxlength="320"
                           required />
                    <label for="emaillogin" class="placeholder-label">Email</label>
                </div>

                <div class="form-group">
                    <input class="email"
                           type="password"
                           id="passwordlogin"
                           name="password"
                           placeholder="Password"
                           maxlength="64"
                           required />
                    <label for="passwordlogin" class="placeholder-label">Password</label>
                </div>

                <div class="options-flex">
                    <label>
                        <input type="checkbox"
                               id="remember-me"
                               name="remember-me"
                               title="Remember me?" />
                        You confirm that you are 18 years of age or older, you are a UK resident, and you agree to our Terms and Conditions & Privacy Policy.
                    </label>

                </div>
                <br />
                <div class="form-group">
                    <turnstile v-if="isLoginVisible"
                               @captchaToken="captchaToken"
                               ref="turnstileContainer" />
                </div>
                <button type="submit"
                        class="submit-btn"
                        :disabled="isSubmitting"
                        @click="validateForm()">
                    <span v-if="!isSubmitting">CREATE AN ACCOUNT</span>
                    <LoaderDots v-if="isSubmitting" />
                </button>
            </div>

         
        </form>
    </div>
</template>

<script lang="ts">
    import { defineComponent, ref } from "vue";


    export default defineComponent({
        components: {

        },
        setup() {






            return {

            };
        },
    });
</script>

<style scoped>
    .error {
        min-height: 20px;
        padding-left: 10px;
    }

    .wrapper {
        padding: 2rem;
    }

    @media (max-width: 420px) {
        .wrapper {
            padding: 1rem;
        }
    }
    @media (max-width: 350px) {
        .wrapper {
            padding-inline: 0.3rem;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

            .form-group input {
                padding: 10px !important;
            }
    }

    input:-webkit-autofill {
        background-color: red !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-container {
        background-color: #ffffffc2;
        border-radius: 0 0 10px 10px;
    }

    .grid {
        display: grid;
        grid-template-columns: auto auto;
        justify-content: space-around;
    }

    .form-title {
        font-size: 32px;
        margin-bottom: 5px;
        color: black;
        font-weight: 600;
        line-height: 1;
        text-align: center;
    }

    .form-text {
        font-size: 14px;
        margin-bottom: 20px;
        color: white;
        text-align: center;
    }

    .form-group {
        position: relative;
        margin-bottom: 15px;
    }

        .form-group label {
            color: var(--clr-black-70);
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-sizing: border-box;
            color: black;
        }

            .form-group input[type="checkbox"] {
                margin-right: 10px;
            }

        .form-group .email:focus {
            outline: 2px solid var(--clr-primary);
        }

    .submit-btn {
        width: 100%;
        padding: 5px 5px;
        background: black;
        border-radius:5px;
        color: white;
        border: none;
        min-height: 77px;
        cursor: pointer;
        font-size: 20px;
        text-transform: uppercase;
    }

        .submit-btn:hover {
            filter: contrast(1.3);
        }

    .options-flex {
        display: flex;
        justify-content: space-between;
        padding-top: 1rem;
        padding-inline: 0.5rem;
    }

        .options-flex input {
            margin-inline: 0.3rem;
        }

    .forgot-password {
        color: black;
    }

        .forgot-password:hover {
            text-decoration: underline;
        }

    input {
        cursor: pointer;
    }

        /* Checkbox Styling */
        input[type="checkbox"] {
            accent-color: var(--clr-tertiary); /* Custom color for the checkbox */
            cursor: pointer;
            transform: scale(1.3); /* Slightly enlarge the checkbox */
        }

        input::placeholder {
            opacity: 0; /* Firefox */
        }

        input:focus::placeholder {
            opacity: 0; /* Firefox */
        }

        input:focus {
            outline: none;
        }

    .placeholder-label {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 16px;
        transition: all 0.2s ease;
        font-size: 18px;
    }

    input:focus ~ .placeholder-label,
    input:not(:placeholder-shown) ~ .placeholder-label {
        top: 13px;
        font-size: 12px;
    }
</style>

