import api from "./axios";
import SignupRequest from "../requests/SingupRequest";
import SigninRequest from "../requests/SigninRequest";
import SigninResponse from "../responses/SigninResponse"; 
import SignupResponse from "../responses/SignupResponse";

class AuthenticationService {
  async signup(signupRequest: SignupRequest) {
    try {
      const response = await api.post("/authentication/register", signupRequest);
      return response.data as SignupResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing up: " + error.message);
      }
    }
  }

  async signin(signinRequest: SigninRequest) {
    try {
      const response = await api.post("/authentication/login", signinRequest);
      return response.data as SigninResponse;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing in: " + error.message);
      }
    }
  }

  async signout() {
    try {
      await api.post("/authentication/logout");      
    } catch (error) {
      if (error instanceof Error) {
        throw new Error("There was an error signing up: " + error.message);
      }
    }
  }
}

export default new AuthenticationService();
