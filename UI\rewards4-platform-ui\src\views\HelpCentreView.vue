<template>
    <section>
        <div class="container">
            <h1>Help Centre</h1>
            <div class="grid">

                <div class="card">
                    <div class="icon">
                        <img src="../assets/images/TEMPDEV/Wallet-icon.svg" />

                    </div>
                    <div class="text">Missing Points</div>
                    <button type="submit">
                        Continue
                        <span class="arrow arrow1">></span><span class="arrow arrow2">></span>
                    </button>
                    <span class="mob-arrow">></span>
                </div>

                <div class="card">
                    <div class="icon">

                        <img src="../assets/images/TEMPDEV/Mail-icon.svg" />
                    </div>
                    <div class="text">Contact Us</div>
                    <button type="submit">
                        Continue
                        <span class="arrow arrow1">></span><span class="arrow arrow2">></span>
                    </button>
                    <span class="mob-arrow">></span>
                </div>

                <div class="card">
                    <div class="icon">
                        <img src="../assets/images/TEMPDEV/faqs-icon.svg" />

                    </div>
                    <div class="text">FAQ</div>
                    <button type="submit">
                        Continue
                        <span class="arrow arrow1">></span><span class="arrow arrow2">></span>
                    </button>
                    <span class="mob-arrow">></span>

                </div>
            </div>
        </div>
    </section>
</template>

<style scoped >
    section {
        background-color: #f3f3f3;
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .container {
        max-width: min(100% - 30px, 1000px); 
        margin-inline: auto;
        padding-block: 1rem;
    }

    h1 {
        text-align: center;
        padding-bottom: 2rem;
        font-size: 34px;
        color: #4c4c4c;
        font-weight: 600;
    }

    .grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap:1rem;
    }

    .card {
        display:grid;
      grid:1rem;
        position: relative; /* so .card-logo positions relative to each card */
        background: #fff;
        margin-bottom: 1rem;
        overflow: hidden;
        box-shadow: 0 7px 15px -7px #333333ab;
        border: solid 2px #fff;
        width: 100%;
        padding:1.5rem;
        align-items: center;
    }



    .icon {
        width: 100px;
        justify-self: center;
    }

    .text {
        font-size: 22px;
        text-align: center;
        color: #4c4c4c;
        font-weight: 600;
        padding-block:1rem;
    }
    .cta {
        width: 100%;
        padding: 1.3rem;
        background-color: black;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 1.1rem;
        cursor: pointer;
        text-align: center;
    }




     button {
        width: 100%;
        padding: 1.3rem;
        background-color: black;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 1.1rem;
        cursor: pointer;
    }


     button {
        transition: letter-spacing 0.5s ease-in-out; /* Smooth animation */
    }

    /* Target span when parent <a> is hovered */
    button:hover {
        letter-spacing: 1.5px;
    }



    .arrow {
        font-size: inherit;
        opacity: 1;
    }

    /* DESKTOP: Animate only on hover */
    button:hover .arrow1,
    button:hover .arrow2 {
        animation: fadeLoop 1s infinite;
    }

    button:hover .arrow2 {
        animation-delay: 0.3s;
    }

    /* MOBILE: Always animate */
    @media (max-width: 768px) {
        .arrow1,
        .arrow2 {
            animation: fadeLoop 1.5s infinite;
        }

        .arrow2 {
            animation-delay: 0.3s;
        }
    }

    /* Keyframes for fade effect */
    @keyframes fadeLoop {
        0%, 100% {
            opacity: 1;
        }

        50% {
            opacity: 0.3;
        }
    }


    .mob-arrow {
        display: none;
    }
    @media (max-width:900px) {
        .grid {
            
            grid-template-columns: 1fr;
            
        }

        .card {
            grid-template-columns: 60px 1fr auto;
            justify-items: self-start;
            gap:1rem;
        }

        .icon {
            width: 60px;
           
        }

        button {
            display: none;
        }

        .mob-arrow {
            display: block;
          
            font-weight: 600;
            color: #4c4c4c;
            font-size: 25px;
        }

    }
</style>