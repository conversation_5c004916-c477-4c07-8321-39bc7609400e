<template>
    <section>
        <div class="container">

            <div class="widget">
                <div class="myaccout-tabs">
                    <input type="radio" checked id="Summary" name="myaccout-tabs" />
                    <label for="Summary">My Statement</label>

                    <input type="radio" id="Terms&Conditions" name="myaccout-tabs" />
                    <label for="Terms&Conditions">Edit Profile</label>

                    <input type="radio" id="htp" name="myaccout-tabs" />
                    <label for="htp">Contact Preferences</label>
                </div>
                <div class="tab-content">


                    <div class="tab-content-1">
                        <mystatement></mystatement>
                    </div>
                      

                    </div>
                    <div class="tab-content-2">
                        <myprofile></myprofile>

                    </div>
                    <div class="tab-content-3">
                        <contactpreferences></contactpreferences>
                    </div>
                </div>
            </div>


         
     
    </section>
</template>


<script lang="ts">
    import { defineComponent, ref } from "vue";

    import myprofile from "../components/MyProfile.vue";
    import contactpreferences from "../components/ContactPreferences.vue";
    import mystatement from "../components/MyStatement.vue";




export default defineComponent({
  components: {
        myprofile,
        contactpreferences,
        mystatement
    
    },
      setup(){



    return {
     
    };

  }
});
</script>

<style scoped>

    section {
        background-color: #f3f3f3;
        background: radial-gradient(#fff 50%, #ecf0f3);
   
    }


    .container {
        max-width: min(100%, 1000px);
        display: grid;
        background-color: white;
        box-shadow: 0 7px 15px -7px #333333ab;
        border: solid 2px #ecf0f3;
        margin-inline: auto;
    }


    .widget {
        padding: 1rem;
        background-color: white;
    }

    .myaccout-tabs {
        --tab-count: 3;
        --active: 0;
        position: relative;
        isolation: isolate;
        display: flex;
        height: 3.5rem;
        border-block-end: 5px solid #7b786538;
        text-align: center;
    }

        .myaccout-tabs input {
            display: none;
        }

        .myaccout-tabs label {
            flex: 1;
            display: inline-block;
            justify-content: center;
            align-items: center;
            align-self: center;
            font-weight: 600;
            color: #7b7865;
            cursor: pointer;
            transition: color 0.5s ease-in-out;
            line-height: 1;
        }

        .myaccout-tabs input:checked + label {
            color: black;
        }

        .myaccout-tabs::after {
            pointer-events: none;
            position: absolute;
            content: "";
            z-index: -1;
            inset: 0 0 -5px;
            width: calc((100% / var(--tab-count)) - 0px);
            border-block-end: 5px var( --clr-primary) solid;
            translate: calc(var(--active) * 100%);
            transition: translate 0.5s ease-in-out;
        }




        .myaccout-tabs:has(:checked:nth-of-type(2)) {
            --active: 0;
        }

        .myaccout-tabs:has(:checked:nth-of-type(2)) {
            --active: 1;
        }

        .myaccout-tabs:has(:checked:nth-of-type(3)) {
            --active: 2;
        }


    .tab-content {
        padding-block: 1rem;
    }


    .tab-content-1 {
        display: none;
    }

    .tab-content-2 {
        display: none;
    }

    .tab-content-3 {
        display: none;
    }

    /* Only target radio buttons in the tab header */
    .widget:has(.myaccout-tabs input[type="radio"]:nth-of-type(1):checked) .tab-content-1 {
        display: block;
    }

    .widget:has(.myaccout-tabs input[type="radio"]:nth-of-type(2):checked) .tab-content-2 {
        display: block;
    }

    .widget:has(.myaccout-tabs input[type="radio"]:nth-of-type(3):checked) .tab-content-3 {
        display: block;
    }

</style>