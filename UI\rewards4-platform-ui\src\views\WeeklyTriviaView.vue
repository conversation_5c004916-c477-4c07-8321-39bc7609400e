<template>
    <section>
        <div class="container">
            <img class="img" src="../assets/images/TEMPDEV/polls (1).png" />
            <div class="title">Weekly Trivia</div>
            <div class="subtitle">Fresh multiple-choice questions every week. Test your knowledge!</div>


            <main>
                <div class="collect-points">COLLECT UP TO 5 POINTS</div>
                <section class="card">

                    <form @submit.prevent>
                        <fieldset>
                            <legend>{{ question.text }}</legend>
                            <div v-for="(choice, i) in question.choices"
                                 :key="i"
                                 class="choice"
                                 @click="selected = i">
                                <input type="radio"
                                       :id="'choice' + i"
                                       :value="i"
                                       v-model="selected" />
                                <label :for="'choice' + i">{{ choice }}</label>
                            </div>
                        </fieldset>


                        <div class="feedback"
                             v-if="answered"
                             :class="{ correct: feedbackType === 'correct', wrong: feedbackType === 'wrong' }">
                            {{ feedback }}
                        </div>


                        <div class="footer">
                            <button type="button"
                                    @click="submitAnswer"
                                    :disabled="answered">
                                Submit & Collect Points       <span class="arrow1">></span><span class="arrow2">></span>
                            </button>
                        </div>
                    </form>
                </section>
            </main>
        </div>
    </section>
</template>

<script setup>
    import { ref } from 'vue'
    import StripBanner from '../components/StripBanner.vue'

    // question data
    const question = {
        text: 'Which planet is known as the Red Planet?',
        choices: ['Venus', 'Mars', 'Jupiter', 'Mercury'],
        answer: 1
    }

    const selected = ref(null)
    const answered = ref(false)
    const feedback = ref('')
    const feedbackType = ref('')
    const score = ref(0)

    function submitAnswer() {
        if (selected.value === null) return
        answered.value = true

        if (selected.value === question.answer) {
            score.value++
            feedback.value = 'Correct! ?'
            feedbackType.value = 'correct'
        } else {
            feedback.value = `Not quite. ? Correct answer: ${question.choices[question.answer]}`
            feedbackType.value = 'wrong'
        }
    }
</script>


<style scoped>

    section {
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .container {
        max-width: 600px;
        margin: auto;
        padding-block: 1rem;
    }

    fieldset {
        border: none;
        padding: 0;
    }

    .collect-points {
        background-color: black;
        color: white;
        padding: 0.5rem;
        text-align: center;
        max-width: 90%;
        border-radius: 10px 10px 0 0;
        margin-inline: auto;
    }

    form {
        padding: 20px;
        background: radial-gradient(#f7f7f7 50%, #ffffff);
    }

    .title {
        font-size: 2rem;
        font-weight: bold;
        text-align: center;
        line-height: 1;
    }


    legend {
        font-size: 18px;
    }

    .subtitle {
        color: #888;
        margin-bottom: 10px;
        text-align: center;
        padding-inline: 1rem;
    }

    .card {
        position: relative;
        background: white;
       
        box-shadow: 0 7px 15px -7px #333333ab;
        border: solid 2px #ecf0f3;
    }

    .img {
        margin-inline: auto;
        width: 100px;
        width: 100px;
    }

    button {
        width: 100%;
        padding: 1.3rem;
        background-color: black;
        color: white;
        border: none;
        border-radius: 5px;
        font-size: 1.1rem;
        cursor: pointer;
        transition: letter-spacing 0.5s ease-in-out; /* Smooth animation */
    }

    .choice {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        margin-bottom: 0.5rem;
        background-color: white;
    }

        
        /* Hide the native radio */
        .choice input[type="radio"] {
            display: none;
        }

        /* Custom circle */
        .choice label {
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            font-size: 1rem;
            line-height: 1.4;
        }

            /* Outer circle */
            .choice label::before {
                content: "";
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 18px;
                height: 18px;
                border: 1px solid #555;
                border-radius: 50%;
                background: #fff;
                transition: border-color 0.2s ease, background 0.2s ease;
            }

        /* Inner dot (hidden by default) */
        .choice input[type="radio"]:checked + label::after {
            content: "";
            position: absolute;
            left: 3px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: red;
            border-radius: 50%;
        }

    .feedback {
        margin-top: 8px;
        font-weight: bold;
    }

    .correct {
        color: green;
    }

    .wrong {
        color: red;
    }

    .footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
    }

    button:hover {
        letter-spacing: 1.5px;
    }

    span {
        font-size: inherit;
        font-weight: 900;
    }

    /* Target span when parent <a> is hovered */
    a:hover {
        letter-spacing: 1.5px;
    }

    .arrow {
        font-size: 20px;
        font-weight: 900;
        opacity: 1;
    }

    /* DESKTOP: Animate only on hover */
    a:hover .arrow1,
    a:hover .arrow2 {
        animation: fadeLoop 1s infinite;
    }

    a:hover .arrow2 {
        animation-delay: 0.3s;
    }

    /* MOBILE: Always animate */
    @media (max-width: 768px) {
        .arrow1,
        .arrow2 {
            animation: fadeLoop 1.5s infinite;
        }

        .arrow2 {
            animation-delay: 0.3s;
        }
    }

    /* Keyframes for fade effect */
    @keyframes fadeLoop {
        0%, 100% {
            opacity: 1;
        }

        50% {
            opacity: 0.3;
        }
    }

    @media(max-width: 600px) {
        .container {
          
            padding-block: 0.5rem 0rem;
        }
    }



</style>