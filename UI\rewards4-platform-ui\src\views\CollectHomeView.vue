<template>
    <div class="bg">
        <div class="grid">
            <category></category>
            <stripBanner></stripBanner>
            <category1></category1>
            <stripBanner></stripBanner>
            <category1></category1>
            
        </div>
    </div>
</template>


<script lang="ts">
    import { defineComponent } from 'vue';

    import heroCarousel from '../components/HeroCarousel.vue';
    import retailerCards from '../components/RetailerCards.vue';
    import masonryCards from '../components/MasonryCards.vue';
    import stripBanner from '../components/StripBanner.vue';
    import category from '../components/CategoryFilter.vue';
    import category1 from '../components/CategorgyFilter1.vue';


    export default defineComponent({
        name: 'home',
        components: {
         
            retailerCards,
            stripBanner,
            masonryCards,
            category,
            category1
        },
    });
</script>

<style scoped>
    .bg {
        background: radial-gradient(#fff 50%, #ecf0f3);
    }

    .grid {
        display: grid;
  
    }
</style>