<template>
    <nav class="nav">
        <div class="container">
            <!-- Logo -->
            <div class="nav-grid">
                <RouterLink class="logo" to="/">
                    <img :src="logo" @click.native="logout" />

                </RouterLink>

                <!-- Not Authenticated -->
                <div v-if="!isAuthenticated" class="login-nav">
                    <RouterLink class="signin-btn"
                                to="/loggedinHome"
                                @click.native="login">
                        Log in
                    </RouterLink>
                    <p>/</p>
                    <RouterLink class="signin-btn" to="/JoinSign">Register</RouterLink>
                </div>

                <!-- Authenticated -->
                <div class="loggedIn" v-if="isAuthenticated">
                    <div class="loggedIn-grid">
                        <div class="flex link-nav">
                            <RouterLink class="signin-btn" to="/home-collect">Collect Points</RouterLink>
                            <p>|</p>
                            <RouterLink class="signin-btn" to="/spend">Spend Points</RouterLink>
                        </div>

                        <RouterLink to="/my-account" class="my-profile">
                            <img class="pofileIcon" :src="pofileIcon" />
                            <div class="balance">&pound;7.75</div>
                        </RouterLink>


                        <!-- Burger Menu -->
                        <div v-if="isAuthenticated" class="burger-menu" @click="toggleMenu">
                            <span :class="{ 'open': isOpen }"></span>
                            <span :class="{ 'open': isOpen }"></span>
                            <span :class="{ 'open': isOpen }"></span>


                        </div>
                    </div>
                </div>
                <input v-if="isAuthenticated" id="search" placeholder="Search for a brand" />
            </div>
        </div>
    </nav>
    <!-- Animated dropdown -->
    <transition name="slide-fade">
        <ul v-if="isOpen && isAuthenticated" class="menu">
            <li><a href="/home-collect">Collect Points</a></li>
            <li><a href="/spend">Spend Points</a></li>
            <li><a href="/">Lincoln City</a></li>
            <li><a href="/winners">Shop Travel</a></li>
            <li><a href="/faqs">Top Partner</a></li>
            <li><a href="/weekly-trivia">Weekly Trivia</a></li>

            <li><a href="/">Competitions</a></li>
        </ul>
    </transition>
    <!-- Quick Links -->
    <div class="quick-links" v-if="isAuthenticated">
        <div class="quick-links-container">
            <RouterLink to="/lincoln-city">Lincoln City</RouterLink>
            <p>|</p>
            <RouterLink to="/spend">Spend Points</RouterLink>
            <p>|</p>
            <RouterLink to="/shop-travel">Shop Travel</RouterLink>
            <p>|</p>
            <RouterLink to="/top-partners">Top Partners</RouterLink>
            <p>|</p>
            <RouterLink to="/weekly-trivia">Weekly Trivia</RouterLink>
            <p>|</p>
            <RouterLink to="/refer-friend">Refer a Friend</RouterLink>
            <p>|</p>
            <RouterLink to="/competitions">Competitions</RouterLink>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent, ref } from 'vue'
    import { useRouter } from 'vue-router'

    export default defineComponent({
        name: 'Nav',
        setup() {
            const router = useRouter()
            const isAuthenticated = ref(false)
            const logo: string = require('../assets/images/logos/LincolnCityLogo.svg')
            const pofileIcon: string = require('../assets/images/TEMPDEV/MyProfile.svg')
            const isOpen = ref(false);

            const toggleMenu = () => {
                isOpen.value = !isOpen.value;
            };

            function login() {
                isAuthenticated.value = true
            }

            function logout() {
                isAuthenticated.value = false
            }

            return { logo, pofileIcon, isAuthenticated, login, logout, toggleMenu, isOpen }
        }
    })
</script>


<style scoped>
    .nav {
        position: relative;
        background-color: var(--clr-primary);
        background: linear-gradient(#e4002b 60%, #e4002b);
        padding-block: 1rem;
        border-bottom: 4px solid var(--clr-secondary);
      
    }

    .container {
        max-width: min(100% - 30px, 1000px);
      
        margin-inline: auto;
        align-items: center;
    }

    .nav-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto;
        column-gap: 1rem;
    }

    .login-nav {
        display: grid;
        grid-template-columns: auto auto auto;
        align-items: center;
        gap: 0.5rem;
        justify-content: end;
        color: white;
        
    }

    .logo {
        display: contents;
     
    }
    .logo img {
           grid-row: 1 / span 0;
    }

   


    .nav img {
        max-height: 70px;
        align-self: center;
    }

    .loggedIn {
        align-self: center;
    }


    .loggedIn-grid {
        display: grid;
        justify-content: end;
        gap: 1rem;
        padding-inline: 0.3rem;
        grid-column: 1 / span 2;
    }

    input {
        width: 100%;
        padding: 0.75rem;
        margin-top: 1rem;
        border: 1px solid #ccc;
        border-radius: 5px;
        grid-column: 1 / span 2;
    }


    .btn-flex p {
        color: white;
        padding-inline: 0.3rem;
        font-weight: 600;
        align-self: center;
        font-size: 20px;
    }

    .loggedIn-grid p {
        color: white;
        padding-inline: 0.3rem;
        font-weight: 600;
        align-self: center;
        font-size: 20px;
    }

    .btn-flex {
        display: flex;
        padding-right: 15px;
    }



    .signin-btn {
        text-decoration: none;
        display: inline;
        color: var(--clr-white);
        border-radius: 15px;
        font-size: 18px;
        justify-self: end;
        align-content: center;
        margin-block: 0.3rem;
    }

    a:hover {
        text-decoration: underline;
    }

    .my-profile {
        text-decoration: none;
        display: flex;
        gap:0.3rem;
        align-items: center;
    
    }
    .my-profile:hover {
        text-decoration: none;      
    }




    .pofileIcon {
        height: 20px;
        color: aliceblue;

    }


    .balance {
        color: white;
        height: 18px;
    }

    .quick-links {
        display: none;
    }

    .burger-menu {
        display: grid;
        gap: 0.4rem;
        align-self: center;
        height: 20px;
        justify-self: end;
      
    }


    .link-nav {
        display: none;
    }



    .burger-menu span {
        width: 32px;
        height: 3px;
        margin: 1px 0;
        border-radius: 50px;
        background-color: var(--clr-white);
        transition: transform 0.3s ease, opacity 0.3s ease;
    }

        .burger-menu span.open:nth-child(1) {
            transform: translateY(12px) rotate(45deg);
        }

        .burger-menu span.open:nth-child(2) {
            opacity: 0;
        }

        .burger-menu span.open:nth-child(3) {
            transform: translateY(-12px) rotate(-45deg);
        }


    /* Add your CSS styles here to style the navbar */
    .menu-items {
        display: flex;
        gap: 2rem;
        align-items: center;
        color: white;
    }

    .menu-item {
        position: relative;
    }

     

   


    .menu-items a {
        text-decoration: none;
        color: var(--clr-black);
        font-size: 18px;
    }

    ul {
        list-style-type: none;
        padding: 0;
    }



    .menu {
        list-style: none;
        background: var(--clr-white);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        left: 0;
        right: 0;
        z-index: 100;
        box-shadow: 0px 6px 8px 0px rgb(0 0 0 / 20%);
        margin-bottom: 1rem;
       
    }

        .menu li {
            cursor: pointer;
            text-decoration: none;
            /*      border-bottom: 2px solid white;*/
    }


            .menu li:not(:first-child) {
                border-top: 2px solid #ecf0f3;
            }


            .menu li a {
                display: flex;
                color: black;
                text-decoration: none;
                padding: 1rem;
            }

    /* Dropdown animation */
    .slide-fade-enter-active {
        transition: all 0.3s ease;
    }

    .slide-fade-leave-active {
        transition: all 0.3s ease;
    }

    .slide-fade-enter-from {
        height: 0;
    }

    .slide-fade-enter-to {
     
  height: 50vh;
    }

    .slide-fade-leave-from {
        height: 50vh;
    }

    .slide-fade-leave-to {
        height: 0;
    }


    @media (min-width: 700px) {
        .quick-links {
            display: block;
            padding-block: 0.1rem;
            background-color: black;
            color: white;
            border-bottom: 4px solid var(--clr-secondary);
        }

        .quick-links-container a {
            color: white;
            text-decoration: none;
        }

        .quick-links-container {
            max-width: min(100% - 30px, 1000px);
            display: flex;
            margin-inline: auto;
            justify-content: space-between;
        }

        .burger-menu, .menu {
         
            display: none;
        }

  


        .link-nav {
            display: flex;
        }


        .container {
            grid-template-columns: 1fr 1fr;
        }


        .login-nav {
           
          
            grid-row: 1 / span 2;
        }


        .loggedIn-grid {
            justify-content: space-between;
            display: flex;
        }

        input {
         
            grid-column: 2 / 2;
        }


        .logo img {
            grid-row: 1 / span 2;
        }

    
        .loggedIn {
            align-self: end;
        }



    }



</style>