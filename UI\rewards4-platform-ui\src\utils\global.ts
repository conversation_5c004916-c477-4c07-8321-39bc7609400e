import Swal from 'sweetalert2';

export function showAlert(
    title: string,
    message: string,
    icon: "success" | "error" | "warning" | "info",
    isHtml: boolean = false,
    onConfirm?: () => void,
    showCloseButton: boolean = false,
    buttonText: string = "OK",
    showDismissButton: boolean = false, 
    dismissButtonText: string = "Decline" 
): void {
    Swal.fire({
        title,
        [isHtml ? "html" : "text"]: message,
        icon,
        confirmButtonText: buttonText,
        showCancelButton: false, 
        background: "var(--clr-white)",
        showCloseButton,
        customClass: {
            confirmButton: "my-custom-button",
            cancelButton: "my-cancel-button", 
        },
        footer: showDismissButton ? '<a href="#" id="custom-cancel" style="display:block; color:#666; text-decoration:none; font-size:14px;">' + dismissButtonText + '</a>' : '',
        didOpen: () => {
            const cancelLink = document.getElementById('custom-cancel')
            if (cancelLink) {
                cancelLink?.addEventListener('click', (e) => {
                    e.preventDefault()
                    Swal.close()
                })
            }
        }
    }).then((result) => {
        if (result.isConfirmed && onConfirm) {
            onConfirm();
        }        
    });
}

export function IsProduction(): boolean {
    return process.env.NODE_ENV === "production";
}