<template>
  <section class="fb-right">
    <div class="right-card">
      <h2 class="display-text">
        YOUR <span>PASSION</span>
        REWARDED.
      </h2>
      <p>
        Our members enjoy big savings on tickets - plus a range of exclusive
        benefits.
      </p>
    </div>

    <div class="right-image">
      <img
        src="../assets/images/TEMPDEV/Lincoln_HomepageLoggedOut_SignIn_HeroBanner_Desktop.png"
      />
      <form class="form-container" @submit.prevent="submitForm">
        <input
          type="text"
          name="name"
          placeholder="Name"
          v-model.trim="signupRequest.name"
          :maxlength="25"
          required
        />
        <small v-if="errors.name" class="error">{{ errors.name }}</small>

        <input
          type="email"
          name="email"
          placeholder="Email"
          v-model.trim="signupRequest.email"
          :maxlength="50"
          required
        />
        <small v-if="errors.email" class="error">{{ errors.email }}</small>

        <input
          type="password"
          name="password"
          placeholder="Password"
          v-model="signupRequest.password"
          :maxlength="50"
          required
        />
        <small v-if="errors.password" class="error">{{
          errors.password
        }}</small>

        <button type="submit" :disabled="isSubmitting">
          {{ isSubmitting ? "Joining..." : "Join for Free" }}
          <span class="arrow arrow1">></span><span class="arrow arrow2">></span>
        </button>

        <small v-if="errors.general" class="error">{{ errors.general }}</small>

        <div class="terms">
          <small>
            Please create me a Rewards4Imps account. I have read, understand and
            agree to the Rewards4Imps Member Agreement, confirm that I am over
            18 years old and understand that my information will be processed
            under the Rewards4Imps Privacy Policy.
          </small>
        </div>
        <div class="terms">
          <small>
            As a Rewards4Imps member, I understand that I will receive email
            marketing communications about points earning opportunities and that
            I may unsubscribe from these communications at any time. For more
            information click here.
          </small>
        </div>
      </form>
    </div>
  </section>
</template>

<script lang="ts">
import { defineComponent, ref } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "../stores/auth";
import SignupRequest from "../requests/SingupRequest";
import SignupResponse from "../responses/SignupResponse";
import { showAlert, IsProduction } from "../utils/global";
import { Urls } from "../constants/Urls";

export default defineComponent({
  components: {},
  setup() {
    const router = useRouter();
    const authStore = useAuthStore();
    const isSubmitting = ref(false);

    const signupRequest = ref<SignupRequest>({
      name: "",
      email: "",
      password: "",
    });

    const errors = ref<{
      name?: string;
      email?: string;
      password?: string;
      general?: string;
    }>({});

    const isValidPassword = (pwd: string) => {
      const hasUpper = /[A-Z]/.test(pwd);
      const hasLower = /[a-z]/.test(pwd);
      const hasDigit = /[0-9]/.test(pwd);
      const hasSpecial = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd);
      return hasUpper && hasLower && hasDigit && hasSpecial;
    };

    const validate = (): boolean => {
      const e: typeof errors.value = {};
      const name = signupRequest.value.name?.trim() ?? "";
      const email = signupRequest.value.email?.trim() ?? "";
      const password = signupRequest.value.password ?? "";

      if (!name) e.name = "Name is required.";
      else if (name.length < 2)
        e.name = "Name must be at least 2 characters long.";
      else if (name.length > 25) e.name = "Name cannot exceed 25 characters.";

      if (!email) e.email = "Email is required.";
      else {
        if (email.length < 6)
          e.email = "Email must be at least 6 characters long.";
        else if (email.length > 50)
          e.email = "Email cannot exceed 50 characters.";
        else {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email))
            e.email = "Invalid email address format.";
        }
      }

      if (!password) e.password = "Password is required.";
      else if (password.length < 8)
        e.password = "Password must be at least 8 characters long.";
      else if (password.length > 50)
        e.password = "Password cannot exceed 64 characters.";
      else if (!isValidPassword(password))
        e.password =
          "Password must include uppercase, lowercase, a number and a special character.";

      errors.value = e;
      return Object.keys(e).length === 0;
    };

    const submitForm = async () => {
      errors.value = {};
      if (!validate()) return;

      try {
        isSubmitting.value = true;
        const response: SignupResponse = await authStore.signup(
          signupRequest.value
        );
        if (response?.isSuccess) {
          router.push("/home");
        } else {
          if (response?.isRacingMember === true) {    
            var rewards4RacingUrl = IsProduction() ? Urls.Rewards4RacingLiveUrl : Urls.Rewards4RacingTestUrl;        
            showAlert(
              "Sign Up Failed",
              `<div>
                <h2>
                  HOLD YOUR HORSES!
                </h2>
              </div>
              <div>
                <p class="mb-0">
                  It looks like you've already got an account with the Rewards4Racing programme in the UK.<br/>
                  Click <a href="${rewards4RacingUrl}" style="text-decoration: underline">here</a> to log-in to your account.
                </p>
                <p class="mb-0">
                  If you live in Ireland and wish to transfer your account, <br/>
                  please contact us at <a href="#" style="text-decoration: underline; cursor: default;"><EMAIL></a>
                </p>
              </div>`,
              "error",
              true
            );
          } else {
            errors.value.general = response?.message || "Registration failed.";
          }
        }
      } catch (err: any) {
        errors.value.general = err?.message || "There was an error signing up.";
      } finally {
        isSubmitting.value = false;
      }
    };

    return {
      signupRequest,
      errors,
      isSubmitting,
      submitForm,
    };
  },
});
</script>

<style scoped>
.bg-image {
  background-size: cover; /* Ensures the image covers the entire container */
  background-position: center; /* Centers the image */
  background-repeat: no-repeat; /* Prevents the image from repeating */

  width: 100vw;
  box-sizing: border-box;
}

.padding {
  padding-block: 1rem;
}

.fb-right {
  color: aliceblue;
  display: grid;
  grid-template-columns: 1fr repeat(12, calc(min(100% - 30px, 1000px) / 12)) 1fr;
  background-color: var(--clr-primary);
  background: radial-gradient(#e4002b 60%, #b50425);
  min-height: 606px;
}

.right-card {
  grid-column: 2 /8;
  border-radius: 5px 0 0 5px;
  align-content: center;
}

.right-card h2 {
  font-size: 90px;
  line-height: 0.9;
  font-weight: 900;
}
.right-card p {
  font-size: 24px;
  line-height: 1;
  max-width: 390px;
}

.right-card span {
  font-weight: 900;
  font-size: 100px;
  color: black;
  font-family: "Caveat Brush";
}

.right-image {
  position: relative;
  grid-column: 8 / -1;
}

.right-image img {
  filter: brightness(0.7) contrast(1.1);
  object-fit: cover;
  height: 100%;
}

.form-container {
  position: absolute;
  top: 50%;
  left: 60px;
  transform: translateY(-50%);
  margin-top: 0.5rem;
  background: rgba(228, 0, 43, 0.85); /* Fixed */
  border: solid 2px rgb(255 255 255 / 43%);
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0.063em 0.75em 1.563em rgba(0, 0, 0, 0.78);
  max-width: 435px;
}

.form-container h2 {
  text-align: center;
  margin-bottom: 1.5rem;
}

.form-container input {
  width: 100%;
  padding: 15px;
  font-size: 16px;
  margin-bottom: 1rem;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.form-container button {
  width: 100%;
  padding: 1.3rem;
  background-color: black;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1.1rem;
  cursor: pointer;
}

.form-container button {
  transition: letter-spacing 0.5s ease-in-out; /* Smooth animation */
}

.terms {
  line-height: 1;
  padding-block: 0.5rem;
  font-size: 13px;
}

/* Target span when parent <a> is hovered */
button:hover {
  letter-spacing: 1.5px;
}

.arrow {
  font-size: inherit;

  opacity: 1;
}

/* DESKTOP: Animate only on hover */
button:hover .arrow1,
button:hover .arrow2 {
  animation: fadeLoop 1s infinite;
}

button:hover .arrow2 {
  animation-delay: 0.3s;
}

/* MOBILE: Always animate */
@media (max-width: 768px) {
  .arrow1,
  .arrow2 {
    animation: fadeLoop 1.5s infinite;
  }

  .arrow2 {
    animation-delay: 0.3s;
  }
}

/* Keyframes for fade effect */
@keyframes fadeLoop {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.3;
  }
}

@media (max-width: 1200px) {
  .form-container {
    left: auto;
    right: 10px;
  }
}

@media (max-width: 830px) {
  .form-container {
    left: 50%;
    top: 0;
    right: auto; /* Important to reset right if you're centering */
    transform: translateX(-50%); /* Use translateX instead of translate */
    width: 95%;
  }

  .fb-right {
    min-height: calc(100vh);
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .right-card {
    max-width: 390px;
    margin-inline: auto;
    padding: 2rem;
    text-align: center;
  }

  .right-card h2 {
    font-size: 60px;
  }
  .right-card span {
    font-size: 65px;
  }

  .right-card p {
    font-size: 20px;
  }
}

@media (max-width: 360px) {
  .right-card h2 {
    font-size: 45px;
  }
  .right-card p {
    font-size: 12px;
    max-width: 180px;
    margin-inline: auto;
  }

  .right-card span {
    font-size: 50px;
  }
}
</style>
